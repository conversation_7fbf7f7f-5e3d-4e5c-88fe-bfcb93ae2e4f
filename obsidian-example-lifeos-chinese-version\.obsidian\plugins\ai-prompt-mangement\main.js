/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var E=Object.defineProperty;var P=Object.getOwnPropertyDescriptor;var L=Object.getOwnPropertyNames;var M=Object.prototype.hasOwnProperty;var S=(h,d)=>{for(var e in d)E(h,e,{get:d[e],enumerable:!0})},K=(h,d,e,t)=>{if(d&&typeof d=="object"||typeof d=="function")for(let n of L(d))!M.call(h,n)&&n!==e&&E(h,n,{get:()=>d[n],enumerable:!(t=P(d,n))||t.enumerable});return h};var A=h=>K(E({},"__esModule",{value:!0}),h);var N={};S(N,{DeleteConfirmModal:()=>F,KanbanView:()=>v,default:()=>y});module.exports=A(N);var s=require("obsidian"),R={activeKanbanFolders:[],showRibbonIcon:!0},b="kanban-view",v=class extends s.ItemView{constructor(e,t,n){super(e);this.searchInputEl=null;this.isComposing=!1;this.fileWatcher=null;this.debounceTimer=null;this.plugin=t,this.folderPath=n}getViewType(){return b+"-"+this.folderPath}getDisplayText(){let e=this.folderPath.split("/");return`Kanban: ${e[e.length-1]}`}getIcon(){return"lucide-plane-takeoff"}async onOpen(){let e=this.containerEl.children[1];e.empty();let t=e.createEl("div",{cls:"kanban-container"}),n=t.createEl("div",{cls:"kanban-actions"}),a=n.createEl("button",{text:o("NewPrompt")});a.onclick=()=>this.createNewFile(),this.searchInputEl=n.createEl("input",{type:"text",placeholder:o("search"),cls:"kanban-search-input"}),this.searchInputEl.addEventListener("compositionstart",()=>this.isComposing=!0),this.searchInputEl.addEventListener("compositionend",()=>{this.isComposing=!1,this.renderKanbanList()}),this.searchInputEl.addEventListener("input",()=>{this.isComposing||this.renderKanbanList()}),t.createEl("ul",{cls:"kanban-list"}),this.renderKanbanList(),this.setupFileWatcher()}setupFileWatcher(){let e=()=>{this.debounceRefresh()};this.plugin.app.vault.on("create",e),this.plugin.app.vault.on("delete",e),this.plugin.app.vault.on("rename",e),this.plugin.app.vault.on("modify",e),this.fileWatcher=()=>{this.plugin.app.vault.off("create",e),this.plugin.app.vault.off("delete",e),this.plugin.app.vault.off("rename",e),this.plugin.app.vault.off("modify",e)}}debounceRefresh(){this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(()=>{this.renderKanbanList()},300)}isFileInCurrentFolder(e){return e.startsWith(this.folderPath+"/")||e===this.folderPath}setupOptimizedFileWatcher(){let e=t=>{t&&t.path&&this.isFileInCurrentFolder(t.path)&&this.debounceRefresh()};this.plugin.app.vault.on("create",e),this.plugin.app.vault.on("delete",e),this.plugin.app.vault.on("rename",e),this.plugin.app.vault.on("modify",e),this.fileWatcher=()=>{this.plugin.app.vault.off("create",e),this.plugin.app.vault.off("delete",e),this.plugin.app.vault.off("rename",e),this.plugin.app.vault.off("modify",e)}}async createNewFile(){if(!(this.app.vault.getAbstractFileByPath(this.folderPath)instanceof s.TFolder)){new s.Notice(o("invalidFolderPath"));return}let n=`Prompt-${new Date().toISOString().slice(0,16).replace(/[-:T]/g,"")}.md`,a=`${this.folderPath}/${n}`;try{let i=`---
prompt-description: \u8BF7\u5728\u6B64\u5904\u63CF\u8FF0\u63D0\u793A\u8BCD\u7684\u7528\u9014
---

# \u63D0\u793A\u8BCD1

## V 1.0

Prompt here...

## V 1.1

# \u63D0\u793A\u8BCD2`,r=await this.app.vault.create(a,i);this.app.workspace.getLeaf(!0).openFile(r),this.renderKanbanList(),new s.Notice(o("fileCreated",n))}catch(i){console.error("Error creating new file:",i),new s.Notice(o("createFileFailed"))}}cleanCodeBlockMarkers(e){return e.replace(/```\s*Markdown\s*\n/g,"").replace(/\n```/g,"").trim()}async renderKanbanList(){var r,u;let e=this.containerEl.querySelector(".kanban-list");if(!e)return;e.empty();let t=this.app.vault.getAbstractFileByPath(this.folderPath);if(!(t instanceof s.TFolder)){this.showEmptyState(e,o("folderInvalidOrNotExists",this.folderPath));return}let n=[];t.children.forEach(l=>{l instanceof s.TFile&&l.extension==="md"&&n.push(l)}),n.sort((l,f)=>f.stat.mtime-l.stat.mtime);let a=((r=this.searchInputEl)==null?void 0:r.value.toLowerCase())||"",i=n.filter(l=>l.basename.toLowerCase().includes(a)||l.name.toLowerCase().includes(a));if(i.length===0){this.showEmptyState(e,a?o("noMatchingFiles",((u=this.searchInputEl)==null?void 0:u.value)||""):o("noMarkdownFiles"));return}for(let l of i)await this.createKanbanItem(e,l)}showEmptyState(e,t){let n=e.createEl("li",{cls:"kanban-empty-state"});n.createEl("h3",{text:o("empty")}),n.createEl("p",{text:t})}async createKanbanItem(e,t){let n=e.createEl("li",{cls:"kanban-list-item"}),a=await this.app.vault.read(t),{version:i,contentSummary:r,versionSpecificContent:u,lastH2LineNumber:l}=this.extractFileInfo(a);n.onclick=g=>{g.target instanceof HTMLElement&&g.target.closest("button")||this.openFileAtVersion(t,l)},n.oncontextmenu=g=>{g.preventDefault(),this.showDeleteMenu(g,t)};let f=n.createEl("div",{cls:"kanban-item-header"});f.createEl("strong",{text:t.basename,cls:"kanban-item-title"});let c=f.createEl("div",{cls:"kanban-item-actions"});this.createActionButtons(c,t,u);let m=n.createEl("div",{cls:"kanban-item-body"});i!=="N/A"&&m.createEl("span",{text:i,cls:"kanban-item-version"}),m.createEl("p",{text:r,cls:"kanban-item-summary"})}extractFileInfo(e){let t=e.split(`
`),n="N/A",a="",i="",r=-1,u=!1,l="",f=0;if(t[0]==="---")for(let c=1;c<t.length;c++){if(t[c]==="---"){f=c+1;break}let m=t[c].match(/^prompt-description:\s*(.+)$/);m&&(l=m[1].trim())}for(let c=t.length-1;c>=f;c--)if(t[c].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)){n=t[c].substring(2).trim(),r=c,u=!0;let g=[];for(let p=c+1;p<t.length&&!(t[p].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)||t[p].match(/^#\s+/));p++)g.push(t[p]);if(i=g.join(`
`).trim(),l)a=l.substring(0,40)+(l.length>40?"...":"");else if(i){let p=this.cleanCodeBlockMarkers(i);a=p.substring(0,40)+(p.length>40?"...":"")}else a="";break}if(!u){if(l)a=l.substring(0,150)+(l.length>150?"...":"");else if(e){let c=this.cleanCodeBlockMarkers(e);a=c.substring(0,150)+(c.length>150?"...":"")}i=e}return{version:n,contentSummary:a,versionSpecificContent:i,lastH2LineNumber:r}}async openFileAtVersion(e,t){let n=this.app.workspace.getLeaf(!1);if(await n.openFile(e),n.view instanceof s.MarkdownView&&t!==-1){let a=n.view;setTimeout(()=>{let i=a.editor;i.setCursor({line:t,ch:0}),i.scrollIntoView({from:{line:t,ch:0},to:{line:t,ch:0}},!0)},200)}}showDeleteMenu(e,t){let n=new s.Menu;n.addItem(a=>a.setTitle(o("delete")).setIcon("trash").onClick(()=>{new F(this.app,o("confirmDeleteFile",t.name),o("deleteWarning"),async()=>{try{await this.app.fileManager.trashFile(t),new s.Notice(o("fileDeleted",t.name)),this.renderKanbanList()}catch(i){console.error("Error deleting file:",i),new s.Notice(o("deleteFileFailed"))}}).open()})),n.showAtMouseEvent(e)}createActionButtons(e,t,n){let a=e.createEl("button",{text:o("iterate")});a.onclick=r=>{r.stopPropagation(),this.iterateFile(t)};let i=e.createEl("button",{text:o("copy")});i.onclick=r=>{r.stopPropagation(),navigator.clipboard.writeText(n),new s.Notice(o("contentCopied"))}}async iterateFile(e){let t=await this.app.vault.read(e),n=t.split(`
`),a="V0.9",i="",r=0;if(n[0]==="---"){for(let p=1;p<n.length;p++)if(n[p]==="---"){r=p+1;break}}for(let p=n.length-1;p>=r;p--){let k=n[p].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i);if(k){let V=k[1],x=k[2]||"0";a=`V${V}.${x}`;let I=[];for(let w=p+1;w<n.length&&!(n[w].match(/^##\s*(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i)||n[w].match(/^#\s+/));w++)I.push(n[w]);i=I.join(`
`).trim();break}}!i&&t.trim()&&(i=n.slice(r).join(`
`).trim());let u=a.match(/(?:V|Version|版本|Ver)?\s*(\d+)(?:\.(\d+))?/i),l=0,f=9;u&&(l=parseInt(u[1]),f=u[2]?parseInt(u[2]):0),f++;let c=`V ${l}.${f}`,m=`
## ${c}
`,g=t+m+(i?i+`
`:`
`);await this.app.vault.modify(e,g),new s.Notice(o("newVersionCreated",c,e.basename)),this.renderKanbanList()}async onClose(){this.fileWatcher&&(this.fileWatcher(),this.fileWatcher=null),this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null),console.log("Kanban view closed, folder path retained in settings for restoration on next startup")}},F=class extends s.Modal{constructor(e,t,n,a){super(e);this.titleText=t,this.messageText=n,this.onConfirm=a}onOpen(){let{contentEl:e}=this;e.empty(),e.createEl("h2",{text:this.titleText}),e.createEl("p",{text:this.messageText});let t=e.createDiv({cls:"modal-button-container"}),n=t.createEl("button",{text:o("confirm"),cls:"mod-cta"});n.onclick=async()=>{await this.onConfirm(),this.close()};let a=t.createEl("button",{text:o("cancel")});a.onclick=()=>this.close()}onClose(){this.contentEl.empty()}},T=class extends s.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),new s.Setting(e).setName(o("showRibbonIcon")).setDesc(o("showRibbonIconDesc")).addToggle(t=>t.setValue(this.plugin.settings.showRibbonIcon).onChange(async n=>{this.plugin.settings.showRibbonIcon=n,await this.plugin.saveSettings(),this.plugin.updateRibbonIcon()}))}},y=class extends s.Plugin{constructor(){super(...arguments);this.ribbonIconEl=null}async onload(){await this.loadSettings();for(let e of this.settings.activeKanbanFolders){let t=b+"-"+e;this.registerView(t,n=>new v(n,this,e))}this.addCommand({id:"generate-kanban-view",name:o("generateKanbanCommand"),editorCallback:(e,t)=>{let n=t.file;n!=null&&n.parent?this.activateView(n.parent.path):new s.Notice(o("cannotGetFolderPath"))}}),this.addSettingTab(new T(this.app,this)),this.updateRibbonIcon(),this.app.workspace.onLayoutReady(()=>this.restoreKanbanViews())}updateRibbonIcon(){this.ribbonIconEl&&(this.ribbonIconEl.remove(),this.ribbonIconEl=null),this.settings.showRibbonIcon&&(this.ribbonIconEl=this.addRibbonIcon("lucide-plane-takeoff",o("ribbonIconTooltip"),()=>{let e=this.app.workspace.getActiveFile();if(e!=null&&e.parent){let t=e.parent.path,n=b+"-"+t,i=this.app.workspace.getLeavesOfType(n).filter(r=>r.view.containerEl&&!r.view.containerEl.hasClass("mod-empty"));i.length>0?(this.app.workspace.revealLeaf(i[0]),new s.Notice(o("kanbanActivated"))):(this.activateView(t),new s.Notice(o("kanbanOpened")))}else new s.Notice(o("cannotGetFolderPath"))}))}async restoreKanbanViews(){var t;if(!((t=this.settings.activeKanbanFolders)!=null&&t.length))return;let e=[];for(let n of this.settings.activeKanbanFolders)if(this.app.vault.getAbstractFileByPath(n)instanceof s.TFolder){let i=b+"-"+n,r=this.app.workspace.getLeavesOfType(i);e.push(n)}e.length!==this.settings.activeKanbanFolders.length&&(this.settings.activeKanbanFolders=e,await this.saveSettings())}async activateView(e,t=!0){let n=b+"-"+e,a=this.app.workspace.getLeavesOfType(n);if(a.length>0){this.app.workspace.revealLeaf(a[0]);return}this.registerView(n,r=>new v(r,this,e));let i=this.app.workspace.getRightLeaf(!1);i?(await i.setViewState({type:n,active:!0}),this.app.workspace.revealLeaf(i),t&&!this.settings.activeKanbanFolders.includes(e)&&(this.settings.activeKanbanFolders.push(e),await this.saveSettings())):new s.Notice(o("cannotOpenKanban"))}onunload(){for(let e of this.settings.activeKanbanFolders){let t=b+"-"+e;this.app.workspace.getLeavesOfType(t).forEach(a=>a.detach())}}async loadSettings(){this.settings=Object.assign({},R,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},C={en:{newFile:"New File",search:"Search...",refresh:"Refresh",iterate:"Iterate",copy:"Copy",delete:"Delete",confirm:"Confirm Delete",cancel:"Cancel",fileCreated:"File {0} created",createFileFailed:"Failed to create file",fileDeleted:'File "{0}" deleted',deleteFileFailed:"Failed to delete file",contentCopied:"Version content copied to clipboard",newVersionCreated:"New version {0} created in file {1}",kanbanActivated:"Kanban view for current folder activated",kanbanOpened:"Kanban view opened for current folder",cannotGetFolderPath:"Cannot get current folder path. Please ensure you are in an open file.",cannotOpenKanban:"Cannot open kanban view. Please ensure there is available panel space on the right.",invalidFolderPath:"Current kanban folder path is invalid",empty:"Empty",noMatchingFiles:'No files matching "{0}" found.',noMarkdownFiles:"This folder has no Markdown files yet.",folderInvalidOrNotExists:"Folder {0} is invalid or does not exist.",confirmDeleteFile:'Are you sure you want to delete file "{0}"?',deleteWarning:"This action cannot be undone. The file will be permanently deleted.",showRibbonIcon:"Show Ribbon Icon",showRibbonIconDesc:"Whether to show the quick button for generating kanban in the sidebar",generateKanbanCommand:"Generate Kanban View for Current Folder",ribbonIconTooltip:"Generate kanban view for current folder"},zh:{NewPrompt:"\u65B0\u589E",search:"\u641C\u7D22...",refresh:"\u5237\u65B0",iterate:"\u8FED\u4EE3",copy:"\u590D\u5236",delete:"\u5220\u9664\u6587\u4EF6",confirm:"\u786E\u8BA4\u5220\u9664",cancel:"\u53D6\u6D88",fileCreated:"\u6587\u4EF6 {0} \u5DF2\u521B\u5EFA",createFileFailed:"\u521B\u5EFA\u6587\u4EF6\u5931\u8D25",fileDeleted:'\u6587\u4EF6 "{0}" \u5DF2\u5220\u9664',deleteFileFailed:"\u5220\u9664\u6587\u4EF6\u5931\u8D25",contentCopied:"\u7248\u672C\u5185\u5BB9\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F",newVersionCreated:"\u65B0\u7248\u672C {0} \u5DF2\u5728\u6587\u4EF6 {1} \u4E2D\u521B\u5EFA",kanbanActivated:"\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE\u5DF2\u6FC0\u6D3B",kanbanOpened:"\u5DF2\u4E3A\u5F53\u524D\u6587\u4EF6\u5939\u6253\u5F00\u770B\u677F\u89C6\u56FE",cannotGetFolderPath:"\u65E0\u6CD5\u83B7\u53D6\u5F53\u524D\u6587\u4EF6\u5939\u8DEF\u5F84\u3002\u8BF7\u786E\u4FDD\u60A8\u5728\u4E00\u4E2A\u6253\u5F00\u7684\u6587\u4EF6\u4E2D\u70B9\u51FB\u6B64\u6309\u94AE\u3002",cannotOpenKanban:"\u65E0\u6CD5\u6253\u5F00\u770B\u677F\u89C6\u56FE\uFF0C\u8BF7\u786E\u4FDD\u53F3\u4FA7\u6709\u53EF\u7528\u7684\u9762\u677F\u7A7A\u95F4\u3002",invalidFolderPath:"\u5F53\u524D\u770B\u677F\u7684\u6587\u4EF6\u5939\u8DEF\u5F84\u65E0\u6548",empty:"\u7A7A\u7A7A\u5982\u4E5F",noMatchingFiles:'\u6CA1\u6709\u627E\u5230\u4E0E "{0}" \u5339\u914D\u7684\u6587\u4EF6\u3002',noMarkdownFiles:"\u8FD9\u4E2A\u6587\u4EF6\u5939\u8FD8\u6CA1\u6709 Markdown \u6587\u4EF6\u3002",folderInvalidOrNotExists:"\u6587\u4EF6\u5939 {0} \u65E0\u6548\u6216\u4E0D\u5B58\u5728\u3002",confirmDeleteFile:'\u60A8\u786E\u5B9A\u8981\u5220\u9664\u6587\u4EF6 "{0}" \u5417\uFF1F',deleteWarning:"\u6B64\u64CD\u4F5C\u65E0\u6CD5\u64A4\u9500\u3002\u6587\u4EF6\u5C06\u88AB\u6C38\u4E45\u5220\u9664\u3002",showRibbonIcon:"\u663E\u793A\u4FA7\u8FB9\u680F\u6309\u94AE",showRibbonIconDesc:"\u662F\u5426\u5728\u4FA7\u8FB9\u680F\u663E\u793A\u751F\u6210\u770B\u677F\u7684\u5FEB\u6377\u6309\u94AE",generateKanbanCommand:"\u751F\u6210\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE",ribbonIconTooltip:"\u751F\u6210\u5F53\u524D\u6587\u4EF6\u5939\u7684\u770B\u677F\u89C6\u56FE"}};function D(){let h=window.moment;return((h==null?void 0:h.locale())||"en").startsWith("zh")?"zh":"en"}function o(h,...d){var n;let e=D(),t=((n=C[e])==null?void 0:n[h])||C.en[h]||h;return d.forEach((a,i)=>{t=t.replace(`{${i}}`,a)}),t}
