/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all3) => {
  for (var name in all3)
    __defProp(target, name, { get: all3[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target, mod));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/.pnpm/short-unique-id@5.0.3/node_modules/short-unique-id/dist/short-unique-id.js
var require_short_unique_id = __commonJS({
  "node_modules/.pnpm/short-unique-id@5.0.3/node_modules/short-unique-id/dist/short-unique-id.js"(exports, module2) {
    "use strict";
    var ShortUniqueId2 = (() => {
      var __defProp2 = Object.defineProperty;
      var __getOwnPropDesc2 = Object.getOwnPropertyDescriptor;
      var __getOwnPropNames2 = Object.getOwnPropertyNames;
      var __getOwnPropSymbols = Object.getOwnPropertySymbols;
      var __hasOwnProp2 = Object.prototype.hasOwnProperty;
      var __propIsEnum = Object.prototype.propertyIsEnumerable;
      var __defNormalProp = (obj, key, value) => key in obj ? __defProp2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
      var __spreadValues = (a, b) => {
        for (var prop in b || (b = {}))
          if (__hasOwnProp2.call(b, prop))
            __defNormalProp(a, prop, b[prop]);
        if (__getOwnPropSymbols)
          for (var prop of __getOwnPropSymbols(b)) {
            if (__propIsEnum.call(b, prop))
              __defNormalProp(a, prop, b[prop]);
          }
        return a;
      };
      var __export2 = (target, all3) => {
        for (var name in all3)
          __defProp2(target, name, { get: all3[name], enumerable: true });
      };
      var __copyProps2 = (to, from, except, desc) => {
        if (from && typeof from === "object" || typeof from === "function") {
          for (let key of __getOwnPropNames2(from))
            if (!__hasOwnProp2.call(to, key) && key !== except)
              __defProp2(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc2(from, key)) || desc.enumerable });
        }
        return to;
      };
      var __toCommonJS2 = (mod) => __copyProps2(__defProp2({}, "__esModule", { value: true }), mod);
      var __publicField = (obj, key, value) => {
        __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
        return value;
      };
      var src_exports = {};
      __export2(src_exports, {
        DEFAULT_OPTIONS: () => DEFAULT_OPTIONS,
        DEFAULT_UUID_LENGTH: () => DEFAULT_UUID_LENGTH,
        default: () => ShortUniqueId3
      });
      var version = "5.0.3";
      var DEFAULT_UUID_LENGTH = 6;
      var DEFAULT_OPTIONS = {
        dictionary: "alphanum",
        shuffle: true,
        debug: false,
        length: DEFAULT_UUID_LENGTH,
        counter: 0
      };
      var _ShortUniqueId = class _ShortUniqueId {
        constructor(argOptions = {}) {
          __publicField(this, "counter");
          __publicField(this, "debug");
          __publicField(this, "dict");
          __publicField(this, "version");
          __publicField(this, "dictIndex", 0);
          __publicField(this, "dictRange", []);
          __publicField(this, "lowerBound", 0);
          __publicField(this, "upperBound", 0);
          __publicField(this, "dictLength", 0);
          __publicField(this, "uuidLength");
          __publicField(this, "_digit_first_ascii", 48);
          __publicField(this, "_digit_last_ascii", 58);
          __publicField(this, "_alpha_lower_first_ascii", 97);
          __publicField(this, "_alpha_lower_last_ascii", 123);
          __publicField(this, "_hex_last_ascii", 103);
          __publicField(this, "_alpha_upper_first_ascii", 65);
          __publicField(this, "_alpha_upper_last_ascii", 91);
          __publicField(this, "_number_dict_ranges", {
            digits: [this._digit_first_ascii, this._digit_last_ascii]
          });
          __publicField(this, "_alpha_dict_ranges", {
            lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],
            upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii]
          });
          __publicField(this, "_alpha_lower_dict_ranges", {
            lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii]
          });
          __publicField(this, "_alpha_upper_dict_ranges", {
            upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii]
          });
          __publicField(this, "_alphanum_dict_ranges", {
            digits: [this._digit_first_ascii, this._digit_last_ascii],
            lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii],
            upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii]
          });
          __publicField(this, "_alphanum_lower_dict_ranges", {
            digits: [this._digit_first_ascii, this._digit_last_ascii],
            lowerCase: [this._alpha_lower_first_ascii, this._alpha_lower_last_ascii]
          });
          __publicField(this, "_alphanum_upper_dict_ranges", {
            digits: [this._digit_first_ascii, this._digit_last_ascii],
            upperCase: [this._alpha_upper_first_ascii, this._alpha_upper_last_ascii]
          });
          __publicField(this, "_hex_dict_ranges", {
            decDigits: [this._digit_first_ascii, this._digit_last_ascii],
            alphaDigits: [this._alpha_lower_first_ascii, this._hex_last_ascii]
          });
          __publicField(this, "_dict_ranges", {
            _number_dict_ranges: this._number_dict_ranges,
            _alpha_dict_ranges: this._alpha_dict_ranges,
            _alpha_lower_dict_ranges: this._alpha_lower_dict_ranges,
            _alpha_upper_dict_ranges: this._alpha_upper_dict_ranges,
            _alphanum_dict_ranges: this._alphanum_dict_ranges,
            _alphanum_lower_dict_ranges: this._alphanum_lower_dict_ranges,
            _alphanum_upper_dict_ranges: this._alphanum_upper_dict_ranges,
            _hex_dict_ranges: this._hex_dict_ranges
          });
          __publicField(this, "log", (...args) => {
            const finalArgs = [...args];
            finalArgs[0] = `[short-unique-id] ${args[0]}`;
            if (this.debug === true) {
              if (typeof console !== "undefined" && console !== null) {
                return console.log(...finalArgs);
              }
            }
          });
          __publicField(this, "setDictionary", (dictionary2, shuffle2) => {
            let finalDict;
            if (dictionary2 && Array.isArray(dictionary2) && dictionary2.length > 1) {
              finalDict = dictionary2;
            } else {
              finalDict = [];
              let i;
              this.dictIndex = i = 0;
              const rangesName = `_${dictionary2}_dict_ranges`;
              const ranges = this._dict_ranges[rangesName];
              Object.keys(ranges).forEach((rangeType) => {
                const rangeTypeKey = rangeType;
                this.dictRange = ranges[rangeTypeKey];
                this.lowerBound = this.dictRange[0];
                this.upperBound = this.dictRange[1];
                for (this.dictIndex = i = this.lowerBound; this.lowerBound <= this.upperBound ? i < this.upperBound : i > this.upperBound; this.dictIndex = this.lowerBound <= this.upperBound ? i += 1 : i -= 1) {
                  finalDict.push(String.fromCharCode(this.dictIndex));
                }
              });
            }
            if (shuffle2) {
              const PROBABILITY = 0.5;
              finalDict = finalDict.sort(() => Math.random() - PROBABILITY);
            }
            this.dict = finalDict;
            this.dictLength = this.dict.length;
            this.setCounter(0);
          });
          __publicField(this, "seq", () => {
            return this.sequentialUUID();
          });
          __publicField(this, "sequentialUUID", () => {
            let counterDiv;
            let counterRem;
            let id = "";
            counterDiv = this.counter;
            do {
              counterRem = counterDiv % this.dictLength;
              counterDiv = Math.trunc(counterDiv / this.dictLength);
              id += this.dict[counterRem];
            } while (counterDiv !== 0);
            this.counter += 1;
            return id;
          });
          __publicField(this, "rnd", (uuidLength = this.uuidLength || DEFAULT_UUID_LENGTH) => {
            return this.randomUUID(uuidLength);
          });
          __publicField(this, "randomUUID", (uuidLength = this.uuidLength || DEFAULT_UUID_LENGTH) => {
            let id;
            let randomPartIdx;
            let j;
            if (uuidLength === null || typeof uuidLength === "undefined" || uuidLength < 1) {
              throw new Error("Invalid UUID Length Provided");
            }
            const isPositive = uuidLength >= 0;
            id = "";
            for (j = 0; j < uuidLength; j += 1) {
              randomPartIdx = parseInt((Math.random() * this.dictLength).toFixed(0), 10) % this.dictLength;
              id += this.dict[randomPartIdx];
            }
            return id;
          });
          __publicField(this, "fmt", (format, date) => {
            return this.formattedUUID(format, date);
          });
          __publicField(this, "formattedUUID", (format, date) => {
            const fnMap = {
              "$r": this.randomUUID,
              "$s": this.sequentialUUID,
              "$t": this.stamp
            };
            const result = format.replace(/\$[rs]\d{0,}|\$t0|\$t[1-9]\d{1,}/g, (m) => {
              const fn = m.slice(0, 2);
              const len = parseInt(m.slice(2), 10);
              if (fn === "$s") {
                return fnMap[fn]().padStart(len, "0");
              }
              if (fn === "$t" && date) {
                return fnMap[fn](len, date);
              }
              return fnMap[fn](len);
            });
            return result;
          });
          __publicField(this, "availableUUIDs", (uuidLength = this.uuidLength) => {
            return parseFloat(Math.pow([...new Set(this.dict)].length, uuidLength).toFixed(0));
          });
          __publicField(this, "approxMaxBeforeCollision", (rounds = this.availableUUIDs(this.uuidLength)) => {
            return parseFloat(Math.sqrt(Math.PI / 2 * rounds).toFixed(20));
          });
          __publicField(this, "collisionProbability", (rounds = this.availableUUIDs(this.uuidLength), uuidLength = this.uuidLength) => {
            return parseFloat((this.approxMaxBeforeCollision(rounds) / this.availableUUIDs(uuidLength)).toFixed(20));
          });
          __publicField(this, "uniqueness", (rounds = this.availableUUIDs(this.uuidLength)) => {
            const score = parseFloat((1 - this.approxMaxBeforeCollision(rounds) / rounds).toFixed(20));
            return score > 1 ? 1 : score < 0 ? 0 : score;
          });
          __publicField(this, "getVersion", () => {
            return this.version;
          });
          __publicField(this, "stamp", (finalLength, date) => {
            const hexStamp = Math.floor(+(date || /* @__PURE__ */ new Date()) / 1e3).toString(16);
            if (typeof finalLength === "number" && finalLength === 0) {
              return hexStamp;
            }
            if (typeof finalLength !== "number" || finalLength < 10) {
              throw new Error([
                "Param finalLength must be a number greater than or equal to 10,",
                "or 0 if you want the raw hexadecimal timestamp"
              ].join("\n"));
            }
            const idLength = finalLength - 9;
            const rndIdx = Math.round(Math.random() * (idLength > 15 ? 15 : idLength));
            const id = this.randomUUID(idLength);
            return `${id.substring(0, rndIdx)}${hexStamp}${id.substring(rndIdx)}${rndIdx.toString(16)}`;
          });
          __publicField(this, "parseStamp", (suid, format) => {
            if (format && !/t0|t[1-9]\d{1,}/.test(format)) {
              throw new Error("Cannot extract date from a formated UUID with no timestamp in the format");
            }
            const stamp = format ? format.replace(/\$[rs]\d{0,}|\$t0|\$t[1-9]\d{1,}/g, (m) => {
              const fnMap = {
                "$r": (len2) => [...Array(len2)].map(() => "r").join(""),
                "$s": (len2) => [...Array(len2)].map(() => "s").join(""),
                "$t": (len2) => [...Array(len2)].map(() => "t").join("")
              };
              const fn = m.slice(0, 2);
              const len = parseInt(m.slice(2), 10);
              return fnMap[fn](len);
            }).replace(/^(.*?)(t{8,})(.*)$/g, (_m, p1, p2) => {
              return suid.substring(p1.length, p1.length + p2.length);
            }) : suid;
            if (stamp.length === 8) {
              return new Date(parseInt(stamp, 16) * 1e3);
            }
            if (stamp.length < 10) {
              throw new Error("Stamp length invalid");
            }
            const rndIdx = parseInt(stamp.substring(stamp.length - 1), 16);
            return new Date(parseInt(stamp.substring(rndIdx, rndIdx + 8), 16) * 1e3);
          });
          __publicField(this, "setCounter", (counter2) => {
            this.counter = counter2;
          });
          const options = __spreadValues(__spreadValues({}, DEFAULT_OPTIONS), argOptions);
          this.counter = 0;
          this.debug = false;
          this.dict = [];
          this.version = version;
          const {
            dictionary,
            shuffle,
            length,
            counter
          } = options;
          this.uuidLength = length;
          this.setDictionary(dictionary, shuffle);
          this.setCounter(counter);
          this.debug = options.debug;
          this.log(this.dict);
          this.log(`Generator instantiated with Dictionary Size ${this.dictLength} and counter set to ${this.counter}`);
          this.log = this.log.bind(this);
          this.setDictionary = this.setDictionary.bind(this);
          this.setCounter = this.setCounter.bind(this);
          this.seq = this.seq.bind(this);
          this.sequentialUUID = this.sequentialUUID.bind(this);
          this.rnd = this.rnd.bind(this);
          this.randomUUID = this.randomUUID.bind(this);
          this.fmt = this.fmt.bind(this);
          this.formattedUUID = this.formattedUUID.bind(this);
          this.availableUUIDs = this.availableUUIDs.bind(this);
          this.approxMaxBeforeCollision = this.approxMaxBeforeCollision.bind(this);
          this.collisionProbability = this.collisionProbability.bind(this);
          this.uniqueness = this.uniqueness.bind(this);
          this.getVersion = this.getVersion.bind(this);
          this.stamp = this.stamp.bind(this);
          this.parseStamp = this.parseStamp.bind(this);
          return this;
        }
      };
      __publicField(_ShortUniqueId, "default", _ShortUniqueId);
      var ShortUniqueId3 = _ShortUniqueId;
      return __toCommonJS2(src_exports);
    })();
    typeof module2 != "undefined" && (module2.exports = ShortUniqueId2.default), typeof window != "undefined" && (ShortUniqueId2 = ShortUniqueId2.default);
  }
});

// main.ts
var main_exports = {};
__export(main_exports, {
  ConfirmUpload: () => ConfirmUpload,
  default: () => driveSyncPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian2 = require("obsidian");

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/bind.js
function bind(fn, thisArg) {
  return function wrap() {
    return fn.apply(thisArg, arguments);
  };
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/utils.js
var { toString } = Object.prototype;
var { getPrototypeOf } = Object;
var kindOf = ((cache) => (thing) => {
  const str = toString.call(thing);
  return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
})(/* @__PURE__ */ Object.create(null));
var kindOfTest = (type) => {
  type = type.toLowerCase();
  return (thing) => kindOf(thing) === type;
};
var typeOfTest = (type) => (thing) => typeof thing === type;
var { isArray } = Array;
var isUndefined = typeOfTest("undefined");
function isBuffer(val) {
  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
}
var isArrayBuffer = kindOfTest("ArrayBuffer");
function isArrayBufferView(val) {
  let result;
  if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
    result = ArrayBuffer.isView(val);
  } else {
    result = val && val.buffer && isArrayBuffer(val.buffer);
  }
  return result;
}
var isString = typeOfTest("string");
var isFunction = typeOfTest("function");
var isNumber = typeOfTest("number");
var isObject = (thing) => thing !== null && typeof thing === "object";
var isBoolean = (thing) => thing === true || thing === false;
var isPlainObject = (val) => {
  if (kindOf(val) !== "object") {
    return false;
  }
  const prototype3 = getPrototypeOf(val);
  return (prototype3 === null || prototype3 === Object.prototype || Object.getPrototypeOf(prototype3) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);
};
var isDate = kindOfTest("Date");
var isFile = kindOfTest("File");
var isBlob = kindOfTest("Blob");
var isFileList = kindOfTest("FileList");
var isStream = (val) => isObject(val) && isFunction(val.pipe);
var isFormData = (thing) => {
  let kind;
  return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
};
var isURLSearchParams = kindOfTest("URLSearchParams");
var trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function forEach(obj, fn, { allOwnKeys = false } = {}) {
  if (obj === null || typeof obj === "undefined") {
    return;
  }
  let i;
  let l;
  if (typeof obj !== "object") {
    obj = [obj];
  }
  if (isArray(obj)) {
    for (i = 0, l = obj.length; i < l; i++) {
      fn.call(null, obj[i], i, obj);
    }
  } else {
    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
    const len = keys.length;
    let key;
    for (i = 0; i < len; i++) {
      key = keys[i];
      fn.call(null, obj[key], key, obj);
    }
  }
}
function findKey(obj, key) {
  key = key.toLowerCase();
  const keys = Object.keys(obj);
  let i = keys.length;
  let _key;
  while (i-- > 0) {
    _key = keys[i];
    if (key === _key.toLowerCase()) {
      return _key;
    }
  }
  return null;
}
var _global = (() => {
  if (typeof globalThis !== "undefined")
    return globalThis;
  return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
})();
var isContextDefined = (context) => !isUndefined(context) && context !== _global;
function merge() {
  const { caseless } = isContextDefined(this) && this || {};
  const result = {};
  const assignValue = (val, key) => {
    const targetKey = caseless && findKey(result, key) || key;
    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
      result[targetKey] = merge(result[targetKey], val);
    } else if (isPlainObject(val)) {
      result[targetKey] = merge({}, val);
    } else if (isArray(val)) {
      result[targetKey] = val.slice();
    } else {
      result[targetKey] = val;
    }
  };
  for (let i = 0, l = arguments.length; i < l; i++) {
    arguments[i] && forEach(arguments[i], assignValue);
  }
  return result;
}
var extend = (a, b, thisArg, { allOwnKeys } = {}) => {
  forEach(b, (val, key) => {
    if (thisArg && isFunction(val)) {
      a[key] = bind(val, thisArg);
    } else {
      a[key] = val;
    }
  }, { allOwnKeys });
  return a;
};
var stripBOM = (content) => {
  if (content.charCodeAt(0) === 65279) {
    content = content.slice(1);
  }
  return content;
};
var inherits = (constructor, superConstructor, props, descriptors2) => {
  constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
  constructor.prototype.constructor = constructor;
  Object.defineProperty(constructor, "super", {
    value: superConstructor.prototype
  });
  props && Object.assign(constructor.prototype, props);
};
var toFlatObject = (sourceObj, destObj, filter2, propFilter) => {
  let props;
  let i;
  let prop;
  const merged = {};
  destObj = destObj || {};
  if (sourceObj == null)
    return destObj;
  do {
    props = Object.getOwnPropertyNames(sourceObj);
    i = props.length;
    while (i-- > 0) {
      prop = props[i];
      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
        destObj[prop] = sourceObj[prop];
        merged[prop] = true;
      }
    }
    sourceObj = filter2 !== false && getPrototypeOf(sourceObj);
  } while (sourceObj && (!filter2 || filter2(sourceObj, destObj)) && sourceObj !== Object.prototype);
  return destObj;
};
var endsWith = (str, searchString, position) => {
  str = String(str);
  if (position === void 0 || position > str.length) {
    position = str.length;
  }
  position -= searchString.length;
  const lastIndex = str.indexOf(searchString, position);
  return lastIndex !== -1 && lastIndex === position;
};
var toArray = (thing) => {
  if (!thing)
    return null;
  if (isArray(thing))
    return thing;
  let i = thing.length;
  if (!isNumber(i))
    return null;
  const arr = new Array(i);
  while (i-- > 0) {
    arr[i] = thing[i];
  }
  return arr;
};
var isTypedArray = ((TypedArray) => {
  return (thing) => {
    return TypedArray && thing instanceof TypedArray;
  };
})(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
var forEachEntry = (obj, fn) => {
  const generator = obj && obj[Symbol.iterator];
  const iterator = generator.call(obj);
  let result;
  while ((result = iterator.next()) && !result.done) {
    const pair = result.value;
    fn.call(obj, pair[0], pair[1]);
  }
};
var matchAll = (regExp, str) => {
  let matches;
  const arr = [];
  while ((matches = regExp.exec(str)) !== null) {
    arr.push(matches);
  }
  return arr;
};
var isHTMLForm = kindOfTest("HTMLFormElement");
var toCamelCase = (str) => {
  return str.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, function replacer(m, p1, p2) {
    return p1.toUpperCase() + p2;
  });
};
var hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
var isRegExp = kindOfTest("RegExp");
var reduceDescriptors = (obj, reducer) => {
  const descriptors2 = Object.getOwnPropertyDescriptors(obj);
  const reducedDescriptors = {};
  forEach(descriptors2, (descriptor, name) => {
    let ret;
    if ((ret = reducer(descriptor, name, obj)) !== false) {
      reducedDescriptors[name] = ret || descriptor;
    }
  });
  Object.defineProperties(obj, reducedDescriptors);
};
var freezeMethods = (obj) => {
  reduceDescriptors(obj, (descriptor, name) => {
    if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name) !== -1) {
      return false;
    }
    const value = obj[name];
    if (!isFunction(value))
      return;
    descriptor.enumerable = false;
    if ("writable" in descriptor) {
      descriptor.writable = false;
      return;
    }
    if (!descriptor.set) {
      descriptor.set = () => {
        throw Error("Can not rewrite read-only method '" + name + "'");
      };
    }
  });
};
var toObjectSet = (arrayOrString, delimiter) => {
  const obj = {};
  const define = (arr) => {
    arr.forEach((value) => {
      obj[value] = true;
    });
  };
  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));
  return obj;
};
var noop = () => {
};
var toFiniteNumber = (value, defaultValue) => {
  value = +value;
  return Number.isFinite(value) ? value : defaultValue;
};
var ALPHA = "abcdefghijklmnopqrstuvwxyz";
var DIGIT = "0123456789";
var ALPHABET = {
  DIGIT,
  ALPHA,
  ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT
};
var generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT) => {
  let str = "";
  const { length } = alphabet;
  while (size--) {
    str += alphabet[Math.random() * length | 0];
  }
  return str;
};
function isSpecCompliantForm(thing) {
  return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === "FormData" && thing[Symbol.iterator]);
}
var toJSONObject = (obj) => {
  const stack = new Array(10);
  const visit = (source, i) => {
    if (isObject(source)) {
      if (stack.indexOf(source) >= 0) {
        return;
      }
      if (!("toJSON" in source)) {
        stack[i] = source;
        const target = isArray(source) ? [] : {};
        forEach(source, (value, key) => {
          const reducedValue = visit(value, i + 1);
          !isUndefined(reducedValue) && (target[key] = reducedValue);
        });
        stack[i] = void 0;
        return target;
      }
    }
    return source;
  };
  return visit(obj, 0);
};
var isAsyncFn = kindOfTest("AsyncFunction");
var isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
var utils_default = {
  isArray,
  isArrayBuffer,
  isBuffer,
  isFormData,
  isArrayBufferView,
  isString,
  isNumber,
  isBoolean,
  isObject,
  isPlainObject,
  isUndefined,
  isDate,
  isFile,
  isBlob,
  isRegExp,
  isFunction,
  isStream,
  isURLSearchParams,
  isTypedArray,
  isFileList,
  forEach,
  merge,
  extend,
  trim,
  stripBOM,
  inherits,
  toFlatObject,
  kindOf,
  kindOfTest,
  endsWith,
  toArray,
  forEachEntry,
  matchAll,
  isHTMLForm,
  hasOwnProperty,
  hasOwnProp: hasOwnProperty,
  reduceDescriptors,
  freezeMethods,
  toObjectSet,
  toCamelCase,
  noop,
  toFiniteNumber,
  findKey,
  global: _global,
  isContextDefined,
  ALPHABET,
  generateString,
  isSpecCompliantForm,
  toJSONObject,
  isAsyncFn,
  isThenable
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/AxiosError.js
function AxiosError(message, code, config, request, response) {
  Error.call(this);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(this, this.constructor);
  } else {
    this.stack = new Error().stack;
  }
  this.message = message;
  this.name = "AxiosError";
  code && (this.code = code);
  config && (this.config = config);
  request && (this.request = request);
  response && (this.response = response);
}
utils_default.inherits(AxiosError, Error, {
  toJSON: function toJSON() {
    return {
      message: this.message,
      name: this.name,
      description: this.description,
      number: this.number,
      fileName: this.fileName,
      lineNumber: this.lineNumber,
      columnNumber: this.columnNumber,
      stack: this.stack,
      config: utils_default.toJSONObject(this.config),
      code: this.code,
      status: this.response && this.response.status ? this.response.status : null
    };
  }
});
var prototype = AxiosError.prototype;
var descriptors = {};
[
  "ERR_BAD_OPTION_VALUE",
  "ERR_BAD_OPTION",
  "ECONNABORTED",
  "ETIMEDOUT",
  "ERR_NETWORK",
  "ERR_FR_TOO_MANY_REDIRECTS",
  "ERR_DEPRECATED",
  "ERR_BAD_RESPONSE",
  "ERR_BAD_REQUEST",
  "ERR_CANCELED",
  "ERR_NOT_SUPPORT",
  "ERR_INVALID_URL"
].forEach((code) => {
  descriptors[code] = { value: code };
});
Object.defineProperties(AxiosError, descriptors);
Object.defineProperty(prototype, "isAxiosError", { value: true });
AxiosError.from = (error, code, config, request, response, customProps) => {
  const axiosError = Object.create(prototype);
  utils_default.toFlatObject(error, axiosError, function filter2(obj) {
    return obj !== Error.prototype;
  }, (prop) => {
    return prop !== "isAxiosError";
  });
  AxiosError.call(axiosError, error.message, code, config, request, response);
  axiosError.cause = error;
  axiosError.name = error.name;
  customProps && Object.assign(axiosError, customProps);
  return axiosError;
};
var AxiosError_default = AxiosError;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/null.js
var null_default = null;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/toFormData.js
function isVisitable(thing) {
  return utils_default.isPlainObject(thing) || utils_default.isArray(thing);
}
function removeBrackets(key) {
  return utils_default.endsWith(key, "[]") ? key.slice(0, -2) : key;
}
function renderKey(path, key, dots) {
  if (!path)
    return key;
  return path.concat(key).map(function each(token, i) {
    token = removeBrackets(token);
    return !dots && i ? "[" + token + "]" : token;
  }).join(dots ? "." : "");
}
function isFlatArray(arr) {
  return utils_default.isArray(arr) && !arr.some(isVisitable);
}
var predicates = utils_default.toFlatObject(utils_default, {}, null, function filter(prop) {
  return /^is[A-Z]/.test(prop);
});
function toFormData(obj, formData, options) {
  if (!utils_default.isObject(obj)) {
    throw new TypeError("target must be an object");
  }
  formData = formData || new (null_default || FormData)();
  options = utils_default.toFlatObject(options, {
    metaTokens: true,
    dots: false,
    indexes: false
  }, false, function defined(option, source) {
    return !utils_default.isUndefined(source[option]);
  });
  const metaTokens = options.metaTokens;
  const visitor = options.visitor || defaultVisitor;
  const dots = options.dots;
  const indexes = options.indexes;
  const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
  const useBlob = _Blob && utils_default.isSpecCompliantForm(formData);
  if (!utils_default.isFunction(visitor)) {
    throw new TypeError("visitor must be a function");
  }
  function convertValue(value) {
    if (value === null)
      return "";
    if (utils_default.isDate(value)) {
      return value.toISOString();
    }
    if (!useBlob && utils_default.isBlob(value)) {
      throw new AxiosError_default("Blob is not supported. Use a Buffer instead.");
    }
    if (utils_default.isArrayBuffer(value) || utils_default.isTypedArray(value)) {
      return useBlob && typeof Blob === "function" ? new Blob([value]) : Buffer.from(value);
    }
    return value;
  }
  function defaultVisitor(value, key, path) {
    let arr = value;
    if (value && !path && typeof value === "object") {
      if (utils_default.endsWith(key, "{}")) {
        key = metaTokens ? key : key.slice(0, -2);
        value = JSON.stringify(value);
      } else if (utils_default.isArray(value) && isFlatArray(value) || (utils_default.isFileList(value) || utils_default.endsWith(key, "[]")) && (arr = utils_default.toArray(value))) {
        key = removeBrackets(key);
        arr.forEach(function each(el, index) {
          !(utils_default.isUndefined(el) || el === null) && formData.append(indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + "[]", convertValue(el));
        });
        return false;
      }
    }
    if (isVisitable(value)) {
      return true;
    }
    formData.append(renderKey(path, key, dots), convertValue(value));
    return false;
  }
  const stack = [];
  const exposedHelpers = Object.assign(predicates, {
    defaultVisitor,
    convertValue,
    isVisitable
  });
  function build(value, path) {
    if (utils_default.isUndefined(value))
      return;
    if (stack.indexOf(value) !== -1) {
      throw Error("Circular reference detected in " + path.join("."));
    }
    stack.push(value);
    utils_default.forEach(value, function each(el, key) {
      const result = !(utils_default.isUndefined(el) || el === null) && visitor.call(formData, el, utils_default.isString(key) ? key.trim() : key, path, exposedHelpers);
      if (result === true) {
        build(el, path ? path.concat(key) : [key]);
      }
    });
    stack.pop();
  }
  if (!utils_default.isObject(obj)) {
    throw new TypeError("data must be an object");
  }
  build(obj);
  return formData;
}
var toFormData_default = toFormData;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/AxiosURLSearchParams.js
function encode(str) {
  const charMap = {
    "!": "%21",
    "'": "%27",
    "(": "%28",
    ")": "%29",
    "~": "%7E",
    "%20": "+",
    "%00": "\0"
  };
  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
    return charMap[match];
  });
}
function AxiosURLSearchParams(params, options) {
  this._pairs = [];
  params && toFormData_default(params, this, options);
}
var prototype2 = AxiosURLSearchParams.prototype;
prototype2.append = function append(name, value) {
  this._pairs.push([name, value]);
};
prototype2.toString = function toString2(encoder) {
  const _encode = encoder ? function(value) {
    return encoder.call(this, value, encode);
  } : encode;
  return this._pairs.map(function each(pair) {
    return _encode(pair[0]) + "=" + _encode(pair[1]);
  }, "").join("&");
};
var AxiosURLSearchParams_default = AxiosURLSearchParams;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/buildURL.js
function encode2(val) {
  return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
}
function buildURL(url, params, options) {
  if (!params) {
    return url;
  }
  const _encode = options && options.encode || encode2;
  const serializeFn = options && options.serialize;
  let serializedParams;
  if (serializeFn) {
    serializedParams = serializeFn(params, options);
  } else {
    serializedParams = utils_default.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams_default(params, options).toString(_encode);
  }
  if (serializedParams) {
    const hashmarkIndex = url.indexOf("#");
    if (hashmarkIndex !== -1) {
      url = url.slice(0, hashmarkIndex);
    }
    url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
  }
  return url;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/InterceptorManager.js
var InterceptorManager = class {
  constructor() {
    this.handlers = [];
  }
  use(fulfilled, rejected, options) {
    this.handlers.push({
      fulfilled,
      rejected,
      synchronous: options ? options.synchronous : false,
      runWhen: options ? options.runWhen : null
    });
    return this.handlers.length - 1;
  }
  eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  }
  clear() {
    if (this.handlers) {
      this.handlers = [];
    }
  }
  forEach(fn) {
    utils_default.forEach(this.handlers, function forEachHandler(h) {
      if (h !== null) {
        fn(h);
      }
    });
  }
};
var InterceptorManager_default = InterceptorManager;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/defaults/transitional.js
var transitional_default = {
  silentJSONParsing: true,
  forcedJSONParsing: true,
  clarifyTimeoutError: false
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/platform/browser/classes/URLSearchParams.js
var URLSearchParams_default = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams_default;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/platform/browser/classes/FormData.js
var FormData_default = typeof FormData !== "undefined" ? FormData : null;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/platform/browser/classes/Blob.js
var Blob_default = typeof Blob !== "undefined" ? Blob : null;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/platform/browser/index.js
var isStandardBrowserEnv = (() => {
  let product;
  if (typeof navigator !== "undefined" && ((product = navigator.product) === "ReactNative" || product === "NativeScript" || product === "NS")) {
    return false;
  }
  return typeof window !== "undefined" && typeof document !== "undefined";
})();
var isStandardBrowserWebWorkerEnv = (() => {
  return typeof WorkerGlobalScope !== "undefined" && self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
})();
var browser_default = {
  isBrowser: true,
  classes: {
    URLSearchParams: URLSearchParams_default,
    FormData: FormData_default,
    Blob: Blob_default
  },
  isStandardBrowserEnv,
  isStandardBrowserWebWorkerEnv,
  protocols: ["http", "https", "file", "blob", "url", "data"]
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/toURLEncodedForm.js
function toURLEncodedForm(data, options) {
  return toFormData_default(data, new browser_default.classes.URLSearchParams(), Object.assign({
    visitor: function(value, key, path, helpers) {
      if (browser_default.isNode && utils_default.isBuffer(value)) {
        this.append(key, value.toString("base64"));
        return false;
      }
      return helpers.defaultVisitor.apply(this, arguments);
    }
  }, options));
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/formDataToJSON.js
function parsePropPath(name) {
  return utils_default.matchAll(/\w+|\[(\w*)]/g, name).map((match) => {
    return match[0] === "[]" ? "" : match[1] || match[0];
  });
}
function arrayToObject(arr) {
  const obj = {};
  const keys = Object.keys(arr);
  let i;
  const len = keys.length;
  let key;
  for (i = 0; i < len; i++) {
    key = keys[i];
    obj[key] = arr[key];
  }
  return obj;
}
function formDataToJSON(formData) {
  function buildPath(path, value, target, index) {
    let name = path[index++];
    const isNumericKey = Number.isFinite(+name);
    const isLast = index >= path.length;
    name = !name && utils_default.isArray(target) ? target.length : name;
    if (isLast) {
      if (utils_default.hasOwnProp(target, name)) {
        target[name] = [target[name], value];
      } else {
        target[name] = value;
      }
      return !isNumericKey;
    }
    if (!target[name] || !utils_default.isObject(target[name])) {
      target[name] = [];
    }
    const result = buildPath(path, value, target[name], index);
    if (result && utils_default.isArray(target[name])) {
      target[name] = arrayToObject(target[name]);
    }
    return !isNumericKey;
  }
  if (utils_default.isFormData(formData) && utils_default.isFunction(formData.entries)) {
    const obj = {};
    utils_default.forEachEntry(formData, (name, value) => {
      buildPath(parsePropPath(name), value, obj, 0);
    });
    return obj;
  }
  return null;
}
var formDataToJSON_default = formDataToJSON;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/defaults/index.js
function stringifySafely(rawValue, parser, encoder) {
  if (utils_default.isString(rawValue)) {
    try {
      (parser || JSON.parse)(rawValue);
      return utils_default.trim(rawValue);
    } catch (e) {
      if (e.name !== "SyntaxError") {
        throw e;
      }
    }
  }
  return (encoder || JSON.stringify)(rawValue);
}
var defaults = {
  transitional: transitional_default,
  adapter: ["xhr", "http"],
  transformRequest: [function transformRequest(data, headers) {
    const contentType = headers.getContentType() || "";
    const hasJSONContentType = contentType.indexOf("application/json") > -1;
    const isObjectPayload = utils_default.isObject(data);
    if (isObjectPayload && utils_default.isHTMLForm(data)) {
      data = new FormData(data);
    }
    const isFormData2 = utils_default.isFormData(data);
    if (isFormData2) {
      if (!hasJSONContentType) {
        return data;
      }
      return hasJSONContentType ? JSON.stringify(formDataToJSON_default(data)) : data;
    }
    if (utils_default.isArrayBuffer(data) || utils_default.isBuffer(data) || utils_default.isStream(data) || utils_default.isFile(data) || utils_default.isBlob(data)) {
      return data;
    }
    if (utils_default.isArrayBufferView(data)) {
      return data.buffer;
    }
    if (utils_default.isURLSearchParams(data)) {
      headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
      return data.toString();
    }
    let isFileList2;
    if (isObjectPayload) {
      if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
        return toURLEncodedForm(data, this.formSerializer).toString();
      }
      if ((isFileList2 = utils_default.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
        const _FormData = this.env && this.env.FormData;
        return toFormData_default(isFileList2 ? { "files[]": data } : data, _FormData && new _FormData(), this.formSerializer);
      }
    }
    if (isObjectPayload || hasJSONContentType) {
      headers.setContentType("application/json", false);
      return stringifySafely(data);
    }
    return data;
  }],
  transformResponse: [function transformResponse(data) {
    const transitional2 = this.transitional || defaults.transitional;
    const forcedJSONParsing = transitional2 && transitional2.forcedJSONParsing;
    const JSONRequested = this.responseType === "json";
    if (data && utils_default.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
      const silentJSONParsing = transitional2 && transitional2.silentJSONParsing;
      const strictJSONParsing = !silentJSONParsing && JSONRequested;
      try {
        return JSON.parse(data);
      } catch (e) {
        if (strictJSONParsing) {
          if (e.name === "SyntaxError") {
            throw AxiosError_default.from(e, AxiosError_default.ERR_BAD_RESPONSE, this, null, this.response);
          }
          throw e;
        }
      }
    }
    return data;
  }],
  timeout: 0,
  xsrfCookieName: "XSRF-TOKEN",
  xsrfHeaderName: "X-XSRF-TOKEN",
  maxContentLength: -1,
  maxBodyLength: -1,
  env: {
    FormData: browser_default.classes.FormData,
    Blob: browser_default.classes.Blob
  },
  validateStatus: function validateStatus(status) {
    return status >= 200 && status < 300;
  },
  headers: {
    common: {
      "Accept": "application/json, text/plain, */*",
      "Content-Type": void 0
    }
  }
};
utils_default.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
  defaults.headers[method] = {};
});
var defaults_default = defaults;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/parseHeaders.js
var ignoreDuplicateOf = utils_default.toObjectSet([
  "age",
  "authorization",
  "content-length",
  "content-type",
  "etag",
  "expires",
  "from",
  "host",
  "if-modified-since",
  "if-unmodified-since",
  "last-modified",
  "location",
  "max-forwards",
  "proxy-authorization",
  "referer",
  "retry-after",
  "user-agent"
]);
var parseHeaders_default = (rawHeaders) => {
  const parsed = {};
  let key;
  let val;
  let i;
  rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
    i = line.indexOf(":");
    key = line.substring(0, i).trim().toLowerCase();
    val = line.substring(i + 1).trim();
    if (!key || parsed[key] && ignoreDuplicateOf[key]) {
      return;
    }
    if (key === "set-cookie") {
      if (parsed[key]) {
        parsed[key].push(val);
      } else {
        parsed[key] = [val];
      }
    } else {
      parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
    }
  });
  return parsed;
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/AxiosHeaders.js
var $internals = Symbol("internals");
function normalizeHeader(header) {
  return header && String(header).trim().toLowerCase();
}
function normalizeValue(value) {
  if (value === false || value == null) {
    return value;
  }
  return utils_default.isArray(value) ? value.map(normalizeValue) : String(value);
}
function parseTokens(str) {
  const tokens = /* @__PURE__ */ Object.create(null);
  const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
  let match;
  while (match = tokensRE.exec(str)) {
    tokens[match[1]] = match[2];
  }
  return tokens;
}
var isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
function matchHeaderValue(context, value, header, filter2, isHeaderNameFilter) {
  if (utils_default.isFunction(filter2)) {
    return filter2.call(this, value, header);
  }
  if (isHeaderNameFilter) {
    value = header;
  }
  if (!utils_default.isString(value))
    return;
  if (utils_default.isString(filter2)) {
    return value.indexOf(filter2) !== -1;
  }
  if (utils_default.isRegExp(filter2)) {
    return filter2.test(value);
  }
}
function formatHeader(header) {
  return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w, char, str) => {
    return char.toUpperCase() + str;
  });
}
function buildAccessors(obj, header) {
  const accessorName = utils_default.toCamelCase(" " + header);
  ["get", "set", "has"].forEach((methodName) => {
    Object.defineProperty(obj, methodName + accessorName, {
      value: function(arg1, arg2, arg3) {
        return this[methodName].call(this, header, arg1, arg2, arg3);
      },
      configurable: true
    });
  });
}
var AxiosHeaders = class {
  constructor(headers) {
    headers && this.set(headers);
  }
  set(header, valueOrRewrite, rewrite) {
    const self2 = this;
    function setHeader(_value, _header, _rewrite) {
      const lHeader = normalizeHeader(_header);
      if (!lHeader) {
        throw new Error("header name must be a non-empty string");
      }
      const key = utils_default.findKey(self2, lHeader);
      if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
        self2[key || _header] = normalizeValue(_value);
      }
    }
    const setHeaders = (headers, _rewrite) => utils_default.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
    if (utils_default.isPlainObject(header) || header instanceof this.constructor) {
      setHeaders(header, valueOrRewrite);
    } else if (utils_default.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
      setHeaders(parseHeaders_default(header), valueOrRewrite);
    } else {
      header != null && setHeader(valueOrRewrite, header, rewrite);
    }
    return this;
  }
  get(header, parser) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils_default.findKey(this, header);
      if (key) {
        const value = this[key];
        if (!parser) {
          return value;
        }
        if (parser === true) {
          return parseTokens(value);
        }
        if (utils_default.isFunction(parser)) {
          return parser.call(this, value, key);
        }
        if (utils_default.isRegExp(parser)) {
          return parser.exec(value);
        }
        throw new TypeError("parser must be boolean|regexp|function");
      }
    }
  }
  has(header, matcher) {
    header = normalizeHeader(header);
    if (header) {
      const key = utils_default.findKey(this, header);
      return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
    }
    return false;
  }
  delete(header, matcher) {
    const self2 = this;
    let deleted = false;
    function deleteHeader(_header) {
      _header = normalizeHeader(_header);
      if (_header) {
        const key = utils_default.findKey(self2, _header);
        if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
          delete self2[key];
          deleted = true;
        }
      }
    }
    if (utils_default.isArray(header)) {
      header.forEach(deleteHeader);
    } else {
      deleteHeader(header);
    }
    return deleted;
  }
  clear(matcher) {
    const keys = Object.keys(this);
    let i = keys.length;
    let deleted = false;
    while (i--) {
      const key = keys[i];
      if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
        delete this[key];
        deleted = true;
      }
    }
    return deleted;
  }
  normalize(format) {
    const self2 = this;
    const headers = {};
    utils_default.forEach(this, (value, header) => {
      const key = utils_default.findKey(headers, header);
      if (key) {
        self2[key] = normalizeValue(value);
        delete self2[header];
        return;
      }
      const normalized = format ? formatHeader(header) : String(header).trim();
      if (normalized !== header) {
        delete self2[header];
      }
      self2[normalized] = normalizeValue(value);
      headers[normalized] = true;
    });
    return this;
  }
  concat(...targets) {
    return this.constructor.concat(this, ...targets);
  }
  toJSON(asStrings) {
    const obj = /* @__PURE__ */ Object.create(null);
    utils_default.forEach(this, (value, header) => {
      value != null && value !== false && (obj[header] = asStrings && utils_default.isArray(value) ? value.join(", ") : value);
    });
    return obj;
  }
  [Symbol.iterator]() {
    return Object.entries(this.toJSON())[Symbol.iterator]();
  }
  toString() {
    return Object.entries(this.toJSON()).map(([header, value]) => header + ": " + value).join("\n");
  }
  get [Symbol.toStringTag]() {
    return "AxiosHeaders";
  }
  static from(thing) {
    return thing instanceof this ? thing : new this(thing);
  }
  static concat(first, ...targets) {
    const computed = new this(first);
    targets.forEach((target) => computed.set(target));
    return computed;
  }
  static accessor(header) {
    const internals = this[$internals] = this[$internals] = {
      accessors: {}
    };
    const accessors = internals.accessors;
    const prototype3 = this.prototype;
    function defineAccessor(_header) {
      const lHeader = normalizeHeader(_header);
      if (!accessors[lHeader]) {
        buildAccessors(prototype3, _header);
        accessors[lHeader] = true;
      }
    }
    utils_default.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
    return this;
  }
};
AxiosHeaders.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
utils_default.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key) => {
  let mapped = key[0].toUpperCase() + key.slice(1);
  return {
    get: () => value,
    set(headerValue) {
      this[mapped] = headerValue;
    }
  };
});
utils_default.freezeMethods(AxiosHeaders);
var AxiosHeaders_default = AxiosHeaders;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/transformData.js
function transformData(fns, response) {
  const config = this || defaults_default;
  const context = response || config;
  const headers = AxiosHeaders_default.from(context.headers);
  let data = context.data;
  utils_default.forEach(fns, function transform(fn) {
    data = fn.call(config, data, headers.normalize(), response ? response.status : void 0);
  });
  headers.normalize();
  return data;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/cancel/isCancel.js
function isCancel(value) {
  return !!(value && value.__CANCEL__);
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/cancel/CanceledError.js
function CanceledError(message, config, request) {
  AxiosError_default.call(this, message == null ? "canceled" : message, AxiosError_default.ERR_CANCELED, config, request);
  this.name = "CanceledError";
}
utils_default.inherits(CanceledError, AxiosError_default, {
  __CANCEL__: true
});
var CanceledError_default = CanceledError;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/settle.js
function settle(resolve, reject, response) {
  const validateStatus2 = response.config.validateStatus;
  if (!response.status || !validateStatus2 || validateStatus2(response.status)) {
    resolve(response);
  } else {
    reject(new AxiosError_default("Request failed with status code " + response.status, [AxiosError_default.ERR_BAD_REQUEST, AxiosError_default.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4], response.config, response.request, response));
  }
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/cookies.js
var cookies_default = browser_default.isStandardBrowserEnv ? function standardBrowserEnv() {
  return {
    write: function write(name, value, expires, path, domain, secure) {
      const cookie = [];
      cookie.push(name + "=" + encodeURIComponent(value));
      if (utils_default.isNumber(expires)) {
        cookie.push("expires=" + new Date(expires).toGMTString());
      }
      if (utils_default.isString(path)) {
        cookie.push("path=" + path);
      }
      if (utils_default.isString(domain)) {
        cookie.push("domain=" + domain);
      }
      if (secure === true) {
        cookie.push("secure");
      }
      document.cookie = cookie.join("; ");
    },
    read: function read(name) {
      const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
      return match ? decodeURIComponent(match[3]) : null;
    },
    remove: function remove(name) {
      this.write(name, "", Date.now() - 864e5);
    }
  };
}() : function nonStandardBrowserEnv() {
  return {
    write: function write() {
    },
    read: function read() {
      return null;
    },
    remove: function remove() {
    }
  };
}();

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/isAbsoluteURL.js
function isAbsoluteURL(url) {
  return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/combineURLs.js
function combineURLs(baseURL, relativeURL) {
  return relativeURL ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/buildFullPath.js
function buildFullPath(baseURL, requestedURL) {
  if (baseURL && !isAbsoluteURL(requestedURL)) {
    return combineURLs(baseURL, requestedURL);
  }
  return requestedURL;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/isURLSameOrigin.js
var isURLSameOrigin_default = browser_default.isStandardBrowserEnv ? function standardBrowserEnv2() {
  const msie = /(msie|trident)/i.test(navigator.userAgent);
  const urlParsingNode = document.createElement("a");
  let originURL;
  function resolveURL(url) {
    let href = url;
    if (msie) {
      urlParsingNode.setAttribute("href", href);
      href = urlParsingNode.href;
    }
    urlParsingNode.setAttribute("href", href);
    return {
      href: urlParsingNode.href,
      protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, "") : "",
      host: urlParsingNode.host,
      search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, "") : "",
      hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, "") : "",
      hostname: urlParsingNode.hostname,
      port: urlParsingNode.port,
      pathname: urlParsingNode.pathname.charAt(0) === "/" ? urlParsingNode.pathname : "/" + urlParsingNode.pathname
    };
  }
  originURL = resolveURL(window.location.href);
  return function isURLSameOrigin(requestURL) {
    const parsed = utils_default.isString(requestURL) ? resolveURL(requestURL) : requestURL;
    return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
  };
}() : function nonStandardBrowserEnv2() {
  return function isURLSameOrigin() {
    return true;
  };
}();

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/parseProtocol.js
function parseProtocol(url) {
  const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
  return match && match[1] || "";
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/speedometer.js
function speedometer(samplesCount, min) {
  samplesCount = samplesCount || 10;
  const bytes = new Array(samplesCount);
  const timestamps = new Array(samplesCount);
  let head = 0;
  let tail = 0;
  let firstSampleTS;
  min = min !== void 0 ? min : 1e3;
  return function push(chunkLength) {
    const now = Date.now();
    const startedAt = timestamps[tail];
    if (!firstSampleTS) {
      firstSampleTS = now;
    }
    bytes[head] = chunkLength;
    timestamps[head] = now;
    let i = tail;
    let bytesCount = 0;
    while (i !== head) {
      bytesCount += bytes[i++];
      i = i % samplesCount;
    }
    head = (head + 1) % samplesCount;
    if (head === tail) {
      tail = (tail + 1) % samplesCount;
    }
    if (now - firstSampleTS < min) {
      return;
    }
    const passed = startedAt && now - startedAt;
    return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
  };
}
var speedometer_default = speedometer;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/adapters/xhr.js
function progressEventReducer(listener, isDownloadStream) {
  let bytesNotified = 0;
  const _speedometer = speedometer_default(50, 250);
  return (e) => {
    const loaded = e.loaded;
    const total = e.lengthComputable ? e.total : void 0;
    const progressBytes = loaded - bytesNotified;
    const rate = _speedometer(progressBytes);
    const inRange = loaded <= total;
    bytesNotified = loaded;
    const data = {
      loaded,
      total,
      progress: total ? loaded / total : void 0,
      bytes: progressBytes,
      rate: rate ? rate : void 0,
      estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
      event: e
    };
    data[isDownloadStream ? "download" : "upload"] = true;
    listener(data);
  };
}
var isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
var xhr_default = isXHRAdapterSupported && function(config) {
  return new Promise(function dispatchXhrRequest(resolve, reject) {
    let requestData = config.data;
    const requestHeaders = AxiosHeaders_default.from(config.headers).normalize();
    const responseType = config.responseType;
    let onCanceled;
    function done() {
      if (config.cancelToken) {
        config.cancelToken.unsubscribe(onCanceled);
      }
      if (config.signal) {
        config.signal.removeEventListener("abort", onCanceled);
      }
    }
    let contentType;
    if (utils_default.isFormData(requestData)) {
      if (browser_default.isStandardBrowserEnv || browser_default.isStandardBrowserWebWorkerEnv) {
        requestHeaders.setContentType(false);
      } else if (!requestHeaders.getContentType(/^\s*multipart\/form-data/)) {
        requestHeaders.setContentType("multipart/form-data");
      } else if (utils_default.isString(contentType = requestHeaders.getContentType())) {
        requestHeaders.setContentType(contentType.replace(/^\s*(multipart\/form-data);+/, "$1"));
      }
    }
    let request = new XMLHttpRequest();
    if (config.auth) {
      const username = config.auth.username || "";
      const password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : "";
      requestHeaders.set("Authorization", "Basic " + btoa(username + ":" + password));
    }
    const fullPath = buildFullPath(config.baseURL, config.url);
    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);
    request.timeout = config.timeout;
    function onloadend() {
      if (!request) {
        return;
      }
      const responseHeaders = AxiosHeaders_default.from("getAllResponseHeaders" in request && request.getAllResponseHeaders());
      const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
      const response = {
        data: responseData,
        status: request.status,
        statusText: request.statusText,
        headers: responseHeaders,
        config,
        request
      };
      settle(function _resolve(value) {
        resolve(value);
        done();
      }, function _reject(err) {
        reject(err);
        done();
      }, response);
      request = null;
    }
    if ("onloadend" in request) {
      request.onloadend = onloadend;
    } else {
      request.onreadystatechange = function handleLoad() {
        if (!request || request.readyState !== 4) {
          return;
        }
        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
          return;
        }
        setTimeout(onloadend);
      };
    }
    request.onabort = function handleAbort() {
      if (!request) {
        return;
      }
      reject(new AxiosError_default("Request aborted", AxiosError_default.ECONNABORTED, config, request));
      request = null;
    };
    request.onerror = function handleError() {
      reject(new AxiosError_default("Network Error", AxiosError_default.ERR_NETWORK, config, request));
      request = null;
    };
    request.ontimeout = function handleTimeout() {
      let timeoutErrorMessage = config.timeout ? "timeout of " + config.timeout + "ms exceeded" : "timeout exceeded";
      const transitional2 = config.transitional || transitional_default;
      if (config.timeoutErrorMessage) {
        timeoutErrorMessage = config.timeoutErrorMessage;
      }
      reject(new AxiosError_default(timeoutErrorMessage, transitional2.clarifyTimeoutError ? AxiosError_default.ETIMEDOUT : AxiosError_default.ECONNABORTED, config, request));
      request = null;
    };
    if (browser_default.isStandardBrowserEnv) {
      const xsrfValue = (config.withCredentials || isURLSameOrigin_default(fullPath)) && config.xsrfCookieName && cookies_default.read(config.xsrfCookieName);
      if (xsrfValue) {
        requestHeaders.set(config.xsrfHeaderName, xsrfValue);
      }
    }
    requestData === void 0 && requestHeaders.setContentType(null);
    if ("setRequestHeader" in request) {
      utils_default.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
        request.setRequestHeader(key, val);
      });
    }
    if (!utils_default.isUndefined(config.withCredentials)) {
      request.withCredentials = !!config.withCredentials;
    }
    if (responseType && responseType !== "json") {
      request.responseType = config.responseType;
    }
    if (typeof config.onDownloadProgress === "function") {
      request.addEventListener("progress", progressEventReducer(config.onDownloadProgress, true));
    }
    if (typeof config.onUploadProgress === "function" && request.upload) {
      request.upload.addEventListener("progress", progressEventReducer(config.onUploadProgress));
    }
    if (config.cancelToken || config.signal) {
      onCanceled = (cancel) => {
        if (!request) {
          return;
        }
        reject(!cancel || cancel.type ? new CanceledError_default(null, config, request) : cancel);
        request.abort();
        request = null;
      };
      config.cancelToken && config.cancelToken.subscribe(onCanceled);
      if (config.signal) {
        config.signal.aborted ? onCanceled() : config.signal.addEventListener("abort", onCanceled);
      }
    }
    const protocol = parseProtocol(fullPath);
    if (protocol && browser_default.protocols.indexOf(protocol) === -1) {
      reject(new AxiosError_default("Unsupported protocol " + protocol + ":", AxiosError_default.ERR_BAD_REQUEST, config));
      return;
    }
    request.send(requestData || null);
  });
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/adapters/adapters.js
var knownAdapters = {
  http: null_default,
  xhr: xhr_default
};
utils_default.forEach(knownAdapters, (fn, value) => {
  if (fn) {
    try {
      Object.defineProperty(fn, "name", { value });
    } catch (e) {
    }
    Object.defineProperty(fn, "adapterName", { value });
  }
});
var renderReason = (reason) => `- ${reason}`;
var isResolvedHandle = (adapter) => utils_default.isFunction(adapter) || adapter === null || adapter === false;
var adapters_default = {
  getAdapter: (adapters) => {
    adapters = utils_default.isArray(adapters) ? adapters : [adapters];
    const { length } = adapters;
    let nameOrAdapter;
    let adapter;
    const rejectedReasons = {};
    for (let i = 0; i < length; i++) {
      nameOrAdapter = adapters[i];
      let id;
      adapter = nameOrAdapter;
      if (!isResolvedHandle(nameOrAdapter)) {
        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
        if (adapter === void 0) {
          throw new AxiosError_default(`Unknown adapter '${id}'`);
        }
      }
      if (adapter) {
        break;
      }
      rejectedReasons[id || "#" + i] = adapter;
    }
    if (!adapter) {
      const reasons = Object.entries(rejectedReasons).map(([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build"));
      let s = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
      throw new AxiosError_default(`There is no suitable adapter to dispatch the request ` + s, "ERR_NOT_SUPPORT");
    }
    return adapter;
  },
  adapters: knownAdapters
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/dispatchRequest.js
function throwIfCancellationRequested(config) {
  if (config.cancelToken) {
    config.cancelToken.throwIfRequested();
  }
  if (config.signal && config.signal.aborted) {
    throw new CanceledError_default(null, config);
  }
}
function dispatchRequest(config) {
  throwIfCancellationRequested(config);
  config.headers = AxiosHeaders_default.from(config.headers);
  config.data = transformData.call(config, config.transformRequest);
  if (["post", "put", "patch"].indexOf(config.method) !== -1) {
    config.headers.setContentType("application/x-www-form-urlencoded", false);
  }
  const adapter = adapters_default.getAdapter(config.adapter || defaults_default.adapter);
  return adapter(config).then(function onAdapterResolution(response) {
    throwIfCancellationRequested(config);
    response.data = transformData.call(config, config.transformResponse, response);
    response.headers = AxiosHeaders_default.from(response.headers);
    return response;
  }, function onAdapterRejection(reason) {
    if (!isCancel(reason)) {
      throwIfCancellationRequested(config);
      if (reason && reason.response) {
        reason.response.data = transformData.call(config, config.transformResponse, reason.response);
        reason.response.headers = AxiosHeaders_default.from(reason.response.headers);
      }
    }
    return Promise.reject(reason);
  });
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/mergeConfig.js
var headersToObject = (thing) => thing instanceof AxiosHeaders_default ? thing.toJSON() : thing;
function mergeConfig(config1, config2) {
  config2 = config2 || {};
  const config = {};
  function getMergedValue(target, source, caseless) {
    if (utils_default.isPlainObject(target) && utils_default.isPlainObject(source)) {
      return utils_default.merge.call({ caseless }, target, source);
    } else if (utils_default.isPlainObject(source)) {
      return utils_default.merge({}, source);
    } else if (utils_default.isArray(source)) {
      return source.slice();
    }
    return source;
  }
  function mergeDeepProperties(a, b, caseless) {
    if (!utils_default.isUndefined(b)) {
      return getMergedValue(a, b, caseless);
    } else if (!utils_default.isUndefined(a)) {
      return getMergedValue(void 0, a, caseless);
    }
  }
  function valueFromConfig2(a, b) {
    if (!utils_default.isUndefined(b)) {
      return getMergedValue(void 0, b);
    }
  }
  function defaultToConfig2(a, b) {
    if (!utils_default.isUndefined(b)) {
      return getMergedValue(void 0, b);
    } else if (!utils_default.isUndefined(a)) {
      return getMergedValue(void 0, a);
    }
  }
  function mergeDirectKeys(a, b, prop) {
    if (prop in config2) {
      return getMergedValue(a, b);
    } else if (prop in config1) {
      return getMergedValue(void 0, a);
    }
  }
  const mergeMap = {
    url: valueFromConfig2,
    method: valueFromConfig2,
    data: valueFromConfig2,
    baseURL: defaultToConfig2,
    transformRequest: defaultToConfig2,
    transformResponse: defaultToConfig2,
    paramsSerializer: defaultToConfig2,
    timeout: defaultToConfig2,
    timeoutMessage: defaultToConfig2,
    withCredentials: defaultToConfig2,
    adapter: defaultToConfig2,
    responseType: defaultToConfig2,
    xsrfCookieName: defaultToConfig2,
    xsrfHeaderName: defaultToConfig2,
    onUploadProgress: defaultToConfig2,
    onDownloadProgress: defaultToConfig2,
    decompress: defaultToConfig2,
    maxContentLength: defaultToConfig2,
    maxBodyLength: defaultToConfig2,
    beforeRedirect: defaultToConfig2,
    transport: defaultToConfig2,
    httpAgent: defaultToConfig2,
    httpsAgent: defaultToConfig2,
    cancelToken: defaultToConfig2,
    socketPath: defaultToConfig2,
    responseEncoding: defaultToConfig2,
    validateStatus: mergeDirectKeys,
    headers: (a, b) => mergeDeepProperties(headersToObject(a), headersToObject(b), true)
  };
  utils_default.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
    const merge2 = mergeMap[prop] || mergeDeepProperties;
    const configValue = merge2(config1[prop], config2[prop], prop);
    utils_default.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
  });
  return config;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/env/data.js
var VERSION = "1.5.1";

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/validator.js
var validators = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach((type, i) => {
  validators[type] = function validator(thing) {
    return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
  };
});
var deprecatedWarnings = {};
validators.transitional = function transitional(validator, version, message) {
  function formatMessage(opt, desc) {
    return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
  }
  return (value, opt, opts) => {
    if (validator === false) {
      throw new AxiosError_default(formatMessage(opt, " has been removed" + (version ? " in " + version : "")), AxiosError_default.ERR_DEPRECATED);
    }
    if (version && !deprecatedWarnings[opt]) {
      deprecatedWarnings[opt] = true;
      console.warn(formatMessage(opt, " has been deprecated since v" + version + " and will be removed in the near future"));
    }
    return validator ? validator(value, opt, opts) : true;
  };
};
function assertOptions(options, schema, allowUnknown) {
  if (typeof options !== "object") {
    throw new AxiosError_default("options must be an object", AxiosError_default.ERR_BAD_OPTION_VALUE);
  }
  const keys = Object.keys(options);
  let i = keys.length;
  while (i-- > 0) {
    const opt = keys[i];
    const validator = schema[opt];
    if (validator) {
      const value = options[opt];
      const result = value === void 0 || validator(value, opt, options);
      if (result !== true) {
        throw new AxiosError_default("option " + opt + " must be " + result, AxiosError_default.ERR_BAD_OPTION_VALUE);
      }
      continue;
    }
    if (allowUnknown !== true) {
      throw new AxiosError_default("Unknown option " + opt, AxiosError_default.ERR_BAD_OPTION);
    }
  }
}
var validator_default = {
  assertOptions,
  validators
};

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/core/Axios.js
var validators2 = validator_default.validators;
var Axios = class {
  constructor(instanceConfig) {
    this.defaults = instanceConfig;
    this.interceptors = {
      request: new InterceptorManager_default(),
      response: new InterceptorManager_default()
    };
  }
  request(configOrUrl, config) {
    if (typeof configOrUrl === "string") {
      config = config || {};
      config.url = configOrUrl;
    } else {
      config = configOrUrl || {};
    }
    config = mergeConfig(this.defaults, config);
    const { transitional: transitional2, paramsSerializer, headers } = config;
    if (transitional2 !== void 0) {
      validator_default.assertOptions(transitional2, {
        silentJSONParsing: validators2.transitional(validators2.boolean),
        forcedJSONParsing: validators2.transitional(validators2.boolean),
        clarifyTimeoutError: validators2.transitional(validators2.boolean)
      }, false);
    }
    if (paramsSerializer != null) {
      if (utils_default.isFunction(paramsSerializer)) {
        config.paramsSerializer = {
          serialize: paramsSerializer
        };
      } else {
        validator_default.assertOptions(paramsSerializer, {
          encode: validators2.function,
          serialize: validators2.function
        }, true);
      }
    }
    config.method = (config.method || this.defaults.method || "get").toLowerCase();
    let contextHeaders = headers && utils_default.merge(headers.common, headers[config.method]);
    headers && utils_default.forEach(["delete", "get", "head", "post", "put", "patch", "common"], (method) => {
      delete headers[method];
    });
    config.headers = AxiosHeaders_default.concat(contextHeaders, headers);
    const requestInterceptorChain = [];
    let synchronousRequestInterceptors = true;
    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
      if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
        return;
      }
      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
    });
    const responseInterceptorChain = [];
    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
    });
    let promise;
    let i = 0;
    let len;
    if (!synchronousRequestInterceptors) {
      const chain = [dispatchRequest.bind(this), void 0];
      chain.unshift.apply(chain, requestInterceptorChain);
      chain.push.apply(chain, responseInterceptorChain);
      len = chain.length;
      promise = Promise.resolve(config);
      while (i < len) {
        promise = promise.then(chain[i++], chain[i++]);
      }
      return promise;
    }
    len = requestInterceptorChain.length;
    let newConfig = config;
    i = 0;
    while (i < len) {
      const onFulfilled = requestInterceptorChain[i++];
      const onRejected = requestInterceptorChain[i++];
      try {
        newConfig = onFulfilled(newConfig);
      } catch (error) {
        onRejected.call(this, error);
        break;
      }
    }
    try {
      promise = dispatchRequest.call(this, newConfig);
    } catch (error) {
      return Promise.reject(error);
    }
    i = 0;
    len = responseInterceptorChain.length;
    while (i < len) {
      promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);
    }
    return promise;
  }
  getUri(config) {
    config = mergeConfig(this.defaults, config);
    const fullPath = buildFullPath(config.baseURL, config.url);
    return buildURL(fullPath, config.params, config.paramsSerializer);
  }
};
utils_default.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
  Axios.prototype[method] = function(url, config) {
    return this.request(mergeConfig(config || {}, {
      method,
      url,
      data: (config || {}).data
    }));
  };
});
utils_default.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
  function generateHTTPMethod(isForm) {
    return function httpMethod(url, data, config) {
      return this.request(mergeConfig(config || {}, {
        method,
        headers: isForm ? {
          "Content-Type": "multipart/form-data"
        } : {},
        url,
        data
      }));
    };
  }
  Axios.prototype[method] = generateHTTPMethod();
  Axios.prototype[method + "Form"] = generateHTTPMethod(true);
});
var Axios_default = Axios;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/cancel/CancelToken.js
var CancelToken = class {
  constructor(executor) {
    if (typeof executor !== "function") {
      throw new TypeError("executor must be a function.");
    }
    let resolvePromise;
    this.promise = new Promise(function promiseExecutor(resolve) {
      resolvePromise = resolve;
    });
    const token = this;
    this.promise.then((cancel) => {
      if (!token._listeners)
        return;
      let i = token._listeners.length;
      while (i-- > 0) {
        token._listeners[i](cancel);
      }
      token._listeners = null;
    });
    this.promise.then = (onfulfilled) => {
      let _resolve;
      const promise = new Promise((resolve) => {
        token.subscribe(resolve);
        _resolve = resolve;
      }).then(onfulfilled);
      promise.cancel = function reject() {
        token.unsubscribe(_resolve);
      };
      return promise;
    };
    executor(function cancel(message, config, request) {
      if (token.reason) {
        return;
      }
      token.reason = new CanceledError_default(message, config, request);
      resolvePromise(token.reason);
    });
  }
  throwIfRequested() {
    if (this.reason) {
      throw this.reason;
    }
  }
  subscribe(listener) {
    if (this.reason) {
      listener(this.reason);
      return;
    }
    if (this._listeners) {
      this._listeners.push(listener);
    } else {
      this._listeners = [listener];
    }
  }
  unsubscribe(listener) {
    if (!this._listeners) {
      return;
    }
    const index = this._listeners.indexOf(listener);
    if (index !== -1) {
      this._listeners.splice(index, 1);
    }
  }
  static source() {
    let cancel;
    const token = new CancelToken(function executor(c) {
      cancel = c;
    });
    return {
      token,
      cancel
    };
  }
};
var CancelToken_default = CancelToken;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/spread.js
function spread(callback) {
  return function wrap(arr) {
    return callback.apply(null, arr);
  };
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/isAxiosError.js
function isAxiosError(payload) {
  return utils_default.isObject(payload) && payload.isAxiosError === true;
}

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/helpers/HttpStatusCode.js
var HttpStatusCode = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
};
Object.entries(HttpStatusCode).forEach(([key, value]) => {
  HttpStatusCode[value] = key;
});
var HttpStatusCode_default = HttpStatusCode;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/lib/axios.js
function createInstance(defaultConfig) {
  const context = new Axios_default(defaultConfig);
  const instance = bind(Axios_default.prototype.request, context);
  utils_default.extend(instance, Axios_default.prototype, context, { allOwnKeys: true });
  utils_default.extend(instance, context, null, { allOwnKeys: true });
  instance.create = function create(instanceConfig) {
    return createInstance(mergeConfig(defaultConfig, instanceConfig));
  };
  return instance;
}
var axios = createInstance(defaults_default);
axios.Axios = Axios_default;
axios.CanceledError = CanceledError_default;
axios.CancelToken = CancelToken_default;
axios.isCancel = isCancel;
axios.VERSION = VERSION;
axios.toFormData = toFormData_default;
axios.AxiosError = AxiosError_default;
axios.Cancel = axios.CanceledError;
axios.all = function all(promises) {
  return Promise.all(promises);
};
axios.spread = spread;
axios.isAxiosError = isAxiosError;
axios.mergeConfig = mergeConfig;
axios.AxiosHeaders = AxiosHeaders_default;
axios.formToJSON = (thing) => formDataToJSON_default(utils_default.isHTMLForm(thing) ? new FormData(thing) : thing);
axios.getAdapter = adapters_default.getAdapter;
axios.HttpStatusCode = HttpStatusCode_default;
axios.default = axios;
var axios_default = axios;

// node_modules/.pnpm/axios@1.5.1/node_modules/axios/index.js
var {
  Axios: Axios2,
  AxiosError: AxiosError2,
  CanceledError: CanceledError2,
  isCancel: isCancel2,
  CancelToken: CancelToken2,
  VERSION: VERSION2,
  all: all2,
  Cancel,
  isAxiosError: isAxiosError2,
  spread: spread2,
  toFormData: toFormData2,
  AxiosHeaders: AxiosHeaders2,
  HttpStatusCode: HttpStatusCode2,
  formToJSON,
  getAdapter,
  mergeConfig: mergeConfig2
} = axios_default;

// main.ts
var import_short_unique_id = __toESM(require_short_unique_id());

// actions.js
var import_obsidian = require("obsidian");
var newError = (actionName, err) => {
  return new Error(`ERROR: Unable to complete action: - ${actionName} => ${err.name} - ${err.message} - ${err.stack}`);
};
var getVaultId = async (accessToken, vault, root = null) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files?q=mimeType%20%3D%20'application%2Fvnd.google-apps.folder'" + (root != null ? `%20and%20'${root}'%20in%20parents` : ""),
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      }
    }).catch((e) => console.log(e));
    const list = response.json.files;
    var vaultFolder = list.filter((file) => file.name == vault);
    var vaultId = vaultFolder.length ? vaultFolder[0].id : "NOT FOUND";
    return vaultId;
  } catch (err) {
    console.log(err);
    throw newError("getVaultId", err);
  }
};
var uploadFile = async (accessToken, fileName, buffer = null, parentId = null) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files?uploadType=multipart",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json",
      body: JSON.stringify({
        name: fileName,
        parents: parentId ? [parentId] : []
      })
    }).catch((e) => console.log(e));
    var id = response.json.id;
    if (buffer) {
      await (0, import_obsidian.requestUrl)({
        url: `https://www.googleapis.com/upload/drive/v3/files/${id}`,
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json"
        },
        contentType: "application/json",
        body: buffer
      }).catch((e) => console.log(e));
    }
    return id;
  } catch (err) {
    console.log(err);
    throw newError("uploadFile", err);
  }
};
var modifyFile = async (accessToken, fileId, buffer) => {
  try {
    var res = await (0, import_obsidian.requestUrl)({
      url: `https://www.googleapis.com/upload/drive/v3/files/${fileId}`,
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json",
      body: buffer
    }).catch((e) => console.log(e));
    return res;
  } catch (err) {
    console.log(err);
    throw newError("modifyFile", err);
  }
};
var renameFile = async (accessToken, fileId, newName) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: `https://www.googleapis.com/drive/v3/files/${fileId}`,
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json",
      body: JSON.stringify({
        name: newName
      })
    }).catch((e) => console.log(e));
    var id = response.json.id;
    return id;
  } catch (err) {
    console.log(err);
    throw newError("renameFile", err);
  }
};
var deleteFile = async (accessToken, fileId) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: `https://www.googleapis.com/drive/v3/files/${fileId}`,
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json"
    });
    if (response.status == 404) {
      return false;
    } else {
      return true;
    }
  } catch (err) {
    if (err.status == 404) {
      return false;
    }
    console.log(err);
    throw newError("deleteFile", err);
  }
};
var uploadFolder = async (accessToken, foldername, rootId = null) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files?uploadType=multipart",
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json",
      body: JSON.stringify({
        mimeType: "application/vnd.google-apps.folder",
        name: foldername,
        parents: rootId ? [rootId] : []
      })
    }).catch((e) => console.log(e));
    var id = response.json.id;
    return id;
  } catch (err) {
    console.log(err);
    throw newError("uploadFolder", err);
  }
};
var getFilesList = async (accessToken, vault) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files" + (vault != null ? `?q='${vault}'%20in%20parents&fields=files(name%2CmodifiedTime%2CmimeType%2Cid)&pageSize=1000` : ""),
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      },
      contentType: "application/json"
    });
    let files = response.json.files;
    let isNextPageAvailable = response.json.nextPageToken ? true : false;
    let nextPageToken = response.json.nextPageToken;
    while (isNextPageAvailable) {
      const response2 = await (0, import_obsidian.requestUrl)({
        url: "https://www.googleapis.com/drive/v3/files" + (vault != null ? `?q='${vault}'%20in%20parents&fields=files(name%2CmodifiedTime%2CmimeType%2Cid)&pageSize=1000` : "") + `&pageToken=${nextPageToken}`,
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json"
        },
        contentType: "application/json"
      });
      files = files.concat(response2.json.files);
      isNextPageAvailable = response2.json.nextPageToken ? true : false;
      nextPageToken = response2.json.nextPageToken;
    }
    return files;
  } catch (err) {
    console.log(err);
    throw newError("getFilesList", err);
  }
};
var getFoldersList = async (accessToken, vault = null) => {
  try {
    const response = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files?q=mimeType%3D%27application%2Fvnd.google-apps.folder%27" + (vault != null ? `%20and%20'${vault}'%20in%20parents` : "") + "&fields=files(name%2Cid)&orderBy=createdTime&pageSize=1000",
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json"
      }
    });
    let folders = response.json.files;
    console.log(folders);
    let isNextPageAvailable = response.json.nextPageToken ? true : false;
    let nextPageToken = response.json.nextPageToken;
    while (isNextPageAvailable) {
      const response2 = await (0, import_obsidian.requestUrl)({
        url: "https://www.googleapis.com/drive/v3/files?q=mimeType%3D%27application%2Fvnd.google-apps.folder%27" + (vault != null ? `%20and%20'${vault}'%20in%20parents` : "") + `&fields=files(name%2Cid)&orderBy=createdTime&pageSize=1000&pageToken=${nextPageToken}`,
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });
      folders = folders.concat(response2.json.files);
      isNextPageAvailable = response2.json.nextPageToken ? true : false;
      nextPageToken = response2.json.nextPageToken;
    }
    return folders;
  } catch (err) {
    console.log(err);
    throw newError("getFoldersList", err);
  }
};
var getFile = async (accessToken, fileId) => {
  try {
    const responseBuffer = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files/" + fileId + "?alt=media",
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    const responseName = await (0, import_obsidian.requestUrl)({
      url: "https://www.googleapis.com/drive/v3/files/" + fileId,
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });
    return [responseName.json.name, responseBuffer.arrayBuffer];
  } catch (err) {
    console.log(err);
    throw newError("getFile", err);
  }
};

// main.ts
var PENDING_SYNC_FILE_NAME = "pendingSync-gdrive-plugin";
var ERROR_LOG_FILE_NAME = "error-log-gdrive-plugin.md";
var VERBOSE_LOG_FILE_NAME = "verbose-log-gdrive-plugin.md";
var ATTACHMENT_TRACKING_FOLDER_NAME = ".attachment-tracking-obsidian-gdrive-sync";
var ignoreFiles = [
  PENDING_SYNC_FILE_NAME,
  ERROR_LOG_FILE_NAME,
  VERBOSE_LOG_FILE_NAME
];
function objectToMap(obj) {
  const map = /* @__PURE__ */ new Map();
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      map.set(key, obj[key]);
    }
  }
  return map;
}
function mapToObject(map) {
  let obj = {};
  for (const [key, value] of map.entries()) {
    obj[key] = value;
  }
  return obj;
}
function bufferEqual(a, b) {
  let c = new Uint8Array(a, 0);
  let d = new Uint8Array(b, 0);
  if (a.byteLength != b.byteLength)
    return false;
  return equal8(c, d);
}
function equal8(a, b) {
  const ua = new Uint8Array(a.buffer, a.byteOffset, a.byteLength);
  const ub = new Uint8Array(b.buffer, b.byteOffset, b.byteLength);
  return compare(ua, ub);
}
function compare(a, b) {
  for (let i = a.length; -1 < i; i -= 1) {
    if (a[i] !== b[i])
      return false;
  }
  return true;
}
var getAccessToken = async (refreshToken, refreshAccessTokenURL, showError = false) => {
  var response;
  await axios_default.post(refreshAccessTokenURL, {
    refreshToken
  }).then((res) => {
    response = res.data;
  }).catch((err) => {
    if ((err.code = "ERR_NETWORK") && showError) {
      new import_obsidian2.Notice("Oops! Network error :(");
      new import_obsidian2.Notice("Or maybe no refresh token provided?", 5e3);
      response = "network_error";
    } else {
      response = "error";
    }
  });
  return response;
};
function removeMergeNotifs() {
  console.log("Calling this!");
  const notices = document.querySelectorAll(".notice");
  notices.forEach((notice) => {
    var _a;
    if ((_a = notice.textContent) == null ? void 0 : _a.includes("has been modified externally")) {
      notice.remove();
      console.log("A merge notice has been removed!");
      return;
    }
  });
}
var { randomUUID } = new import_short_unique_id.default({ length: 6 });
var DEFAULT_SETTINGS = {
  refreshToken: "",
  accessToken: "",
  accessTokenExpiryTime: "",
  refreshAccessTokenURL: "https://red-formula-303406.ue.r.appspot.com/auth/obsidian/refresh-token",
  fetchRefreshTokenURL: "https://red-formula-303406.ue.r.appspot.com/auth/obsidian",
  validToken: false,
  vaultId: "",
  filesList: [],
  vaultInit: false,
  rootFolderId: "",
  refresh: false,
  refreshTime: "5",
  autoRefreshBinaryFiles: "1",
  errorLoggingToFile: false,
  verboseLoggingToFile: false,
  blacklistPaths: [],
  forceFocus: false,
  removeMergeNotifsSettings: false
};
var metaPattern = /^---\n[\s\S]*---/;
var driveDataPattern = /\nlastSync:.*\n/;
var driveSyncPlugin = class extends import_obsidian2.Plugin {
  constructor() {
    super(...arguments);
    this.cloudFiles = [];
    this.localFiles = [];
    this.timer = null;
    this.alreadyRefreshing = false;
    this.writingFile = false;
    this.syncQueue = [];
    this.isUploadingCurrentFile = false;
    this.latestContentThatWasSynced = null;
    this.currentlyUploading = null;
    this.renamingList = [];
    this.deletingList = [];
    this.statusBarItem = this.addStatusBarItem().createEl("span", "sync_icon_still");
    this.pendingSync = false;
    this.connectedToInternet = false;
    this.checkingForConnectivity = false;
    this.pendingSyncItems = [];
    this.renamedWhileOffline = /* @__PURE__ */ new Map();
    this.finalNamesForFileID = /* @__PURE__ */ new Map();
    this.completingPendingSync = false;
    this.verboseLoggingForTheFirstTimeInThisSession = true;
    this.errorLoggingForTheFirstTimeInThisSession = true;
    this.lastErrorTime = new Date(0);
    this.totalErrorsWithinAMinute = 0;
    this.haltAllOperations = false;
    this.attachmentTrackingInitializationComplete = false;
    this.layoutReady = false;
    this.completeAllPendingSyncs = async () => {
      if (!this.app.workspace.layoutReady) {
        return;
      }
      if (this.haltAllOperations) {
        return;
      }
      if (this.completingPendingSync) {
        return;
      }
      await this.writeToVerboseLogFile("LOG: Entering completeAllPendingSyncs");
      let uuidToFileIdMap = /* @__PURE__ */ new Map();
      let pendingSyncFile = this.app.vault.getAbstractFileByPath(PENDING_SYNC_FILE_NAME);
      pendingSyncFile instanceof import_obsidian2.TFile ? console.log(JSON.parse(await this.app.vault.read(pendingSyncFile))) : console.log("No file");
      let {
        pendingSyncItems,
        finalNamesForFileID
      } = pendingSyncFile instanceof import_obsidian2.TFile ? JSON.parse(await this.app.vault.read(pendingSyncFile)) : { pendingSyncItems: [], finalNamesForFileID: /* @__PURE__ */ new Map() };
      this.pendingSyncItems = [...pendingSyncItems];
      this.finalNamesForFileID = objectToMap(finalNamesForFileID);
      let finalNamesForFileIDMap = objectToMap(finalNamesForFileID);
      console.log(pendingSyncItems, finalNamesForFileID);
      if (pendingSyncItems.length) {
        new import_obsidian2.Notice("ATTENTION: Syncing all pending changes since app was last online!");
        new import_obsidian2.Notice("Please wait till the sync is complete before proceeding with anything else...");
      }
      try {
        this.settings.filesList = await getFilesList(this.settings.accessToken, this.settings.vaultId);
        this.completingPendingSync = true;
        for (var item of pendingSyncItems) {
          let lastCloudUpdateTime = new Date(0);
          let pendingSyncTime = new Date(item.timeStamp);
          this.settings.filesList.forEach((file2) => {
            if (file2.id == item.fileID) {
              lastCloudUpdateTime = new Date(file2.modifiedTime);
            }
          });
          switch (item.action) {
            case "DELETE":
              if (lastCloudUpdateTime < pendingSyncTime) {
                await deleteFile(this.settings.accessToken, uuidToFileIdMap.get(item.fileID) ? uuidToFileIdMap.get(item.fileID) : item.fileID);
              }
              await this.writeToVerboseLogFile("LOG: Deleted file. [PS]");
              break;
            case "UPLOAD":
              var fileName = finalNamesForFileIDMap.get(item.fileID);
              var file = this.app.vault.getAbstractFileByPath(fileName);
              let actualId;
              if (file instanceof import_obsidian2.TFile) {
                if (item.isBinaryFile) {
                  actualId = await this.uploadNewAttachment(file);
                } else {
                  actualId = await this.uploadNewNotesFile(file);
                }
              }
              uuidToFileIdMap.set(item.fileID, actualId);
              finalNamesForFileIDMap.set(actualId, fileName);
              this.finalNamesForFileID.set(actualId, fileName);
              await this.writeToVerboseLogFile("LOG: Uploaded file. [PS]");
              break;
            case "MODIFY":
              if (pendingSyncTime > lastCloudUpdateTime) {
                let file2 = this.app.vault.getAbstractFileByPath(finalNamesForFileIDMap.get(item.fileID));
                if (file2 instanceof import_obsidian2.TFile) {
                  await this.updateLastSyncMetaTag(file2);
                  var buffer = await this.app.vault.readBinary(file2);
                  await modifyFile(this.settings.accessToken, uuidToFileIdMap.get(item.fileID) ? uuidToFileIdMap.get(item.fileID) : item.fileID, buffer);
                }
                await this.writeToVerboseLogFile("LOG: Modified file. [PS]");
              }
              break;
            case "RENAME":
              if (pendingSyncTime > lastCloudUpdateTime) {
                await renameFile(this.settings.accessToken, uuidToFileIdMap.get(item.fileID) ? uuidToFileIdMap.get(item.fileID) : item.fileID, finalNamesForFileIDMap.get(item.fileID));
              }
              await this.writeToVerboseLogFile("LOG: Renamed file. [PS]");
              break;
          }
          this.pendingSyncItems.shift();
          await this.writeToPendingSyncFile();
          new import_obsidian2.Notice(`Synced ${pendingSyncItems.indexOf(item) + 1}/${pendingSyncItems.length} changes`);
          await this.writeToVerboseLogFile("LOG: completeAllPendingSyncs: Finished one operation");
        }
      } catch (err) {
        if (err.message.includes("404")) {
          this.pendingSyncItems.shift();
          await this.writeToPendingSyncFile();
        }
        this.completingPendingSync = false;
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
      if (pendingSyncItems.length) {
        new import_obsidian2.Notice("Sync complete!");
        this.finalNamesForFileID.clear();
        await this.writeToPendingSyncFile();
        await this.writeToVerboseLogFile("LOG: completeAllPendingSyncs: Finished allpendingSyncs");
      }
      this.completingPendingSync = false;
      this.pendingSync = false;
      await this.refreshAll();
      await this.writeToVerboseLogFile("LOG: Exited completeAllPendingSyncs");
    };
    this.checkForConnectivity = async () => {
      if (this.haltAllOperations) {
        return;
      }
      try {
        await this.writeToVerboseLogFile("LOG: Entering checkForConnectivity");
        await fetch("https://www.github.com/stravo1", {
          mode: "no-cors"
        });
        if (!this.connectedToInternet) {
          new import_obsidian2.Notice("Connectivity re-established!");
          this.connectedToInternet = true;
          this.checkingForConnectivity = false;
        }
        await this.completeAllPendingSyncs();
      } catch (err) {
        console.log("Checking for connectivity again after 5sec...");
        if (this.connectedToInternet) {
          console.log("error: " + err);
          new import_obsidian2.Notice("Connection lost :(");
          this.connectedToInternet = false;
          await this.writeToErrorLogFile(err);
        }
        setTimeout(() => {
          this.checkingForConnectivity = true;
          this.checkForConnectivity();
        }, 5e3);
      }
      await this.writeToVerboseLogFile("LOG: Exited checkForConnectivity");
    };
    this.notifyError = async () => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      if (this.haltAllOperations) {
        return;
      }
      if (!this.pendingSync) {
        this.pendingSync = true;
        new import_obsidian2.Notice("ERROR: Something went wrong! Sync might be paused!");
      }
      await this.writeToVerboseLogFile("LOG: Error occured");
      if (new Date().getTime() - this.lastErrorTime.getTime() < 6e4) {
        this.totalErrorsWithinAMinute++;
      } else {
        this.totalErrorsWithinAMinute = 0;
      }
      if (this.totalErrorsWithinAMinute > 5) {
        this.haltAllOperations = true;
        setTimeout(async () => {
          await this.writeToErrorLogFile(new Error("FATAL ERROR: Too many errors within a minute."));
          await this.writeToVerboseLogFile("LOG: Too many errors within a minute. Halting all operations.");
          new import_obsidian2.Notice("FATAL ERROR: Too many errors within a minute. Please reload the plug-in. If error persists, check the Verbose and Error Logs (turn them on in plug-in settings).", 5e3);
          new import_obsidian2.Notice("Report issue by attaching the log files at https://github.com/stravo1/obsidian-gdrive-sync/issues/new", 5e3);
        }, 1500);
      }
      this.lastErrorTime = new Date();
    };
    this.cleanInstall = async () => {
      if (this.haltAllOperations) {
        return;
      }
      try {
        await this.writeToVerboseLogFile("LOG: Enerting cleanInstall");
        if (!this.settings.rootFolderId) {
          await this.writeToErrorLogFile(new Error("ERROR: Root folder does not exist"));
          new import_obsidian2.Notice("ERROR: Root folder does not exist. Please reload the plug-in.");
          new import_obsidian2.Notice("If this error persists, please check if there is a folder named 'obsidian' in your Google Drive.");
          new import_obsidian2.Notice("If there is one and you are still getting this error, consider joining the Discord server for help.", 3e3);
          new import_obsidian2.Notice("If there is no folder named 'obsidian' in your Drive root, try using the 'Create root folder' button in Settings.", 4e3);
          return;
        }
        new import_obsidian2.Notice("Creating vault in Google Drive...");
        var res = await uploadFolder(this.settings.accessToken, this.app.vault.getName(), this.settings.rootFolderId);
        this.settings.vaultId = res;
        new import_obsidian2.Notice("Vault created!");
        new import_obsidian2.Notice("Uploading files, this might take time. Please wait...", 6e3);
        var filesList = this.app.vault.getFiles();
        let noOfFiles = filesList.length;
        let count = 0;
        for (const file of filesList) {
          if (file.extension != "md") {
            await this.uploadNewAttachment(file);
          } else {
            await this.uploadNewNotesFile(file);
          }
          count++;
          new import_obsidian2.Notice("Uploaded " + count + "/" + noOfFiles + " files");
        }
        new import_obsidian2.Notice("Files uploaded!");
        new import_obsidian2.Notice("Please reload the plug-in.", 5e3);
      } catch (err) {
        new import_obsidian2.Notice("ERROR: Unable to initialize Vault in Google Drive");
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
      await this.writeToVerboseLogFile("LOG: Exited cleanInstall");
    };
    this.refreshAll = async () => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      if (this.haltAllOperations) {
        return;
      }
      await this.writeToVerboseLogFile("LOG: Entering refreshAll");
      try {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not refreshing...");
          return;
        }
        if (new Date(this.settings.accessTokenExpiryTime).getTime() - new Date().getTime() < 18e5) {
          await this.writeToVerboseLogFile("LOG: Token will expire in 30mins, getting new token...");
          var res = await getAccessToken(this.settings.refreshToken, this.settings.refreshAccessTokenURL, false);
          if (res == "error") {
            new import_obsidian2.Notice("ERROR: Couldn't fetch new accessToken :(");
            await this.writeToErrorLogFile(new Error("ERROR: Couldn't fetch new accessToken"));
            return;
          }
          this.settings.accessToken = res.access_token;
          this.settings.accessTokenExpiryTime = res.expiry_date;
          this.saveSettings();
        }
        if (this.pendingSync) {
          console.log("PAUSED: Writing pending syncs, not refreshing...");
          if (!this.checkingForConnectivity) {
            setTimeout(() => {
              this.checkForConnectivity();
            }, 5e3);
          }
          return;
        }
        if (this.alreadyRefreshing) {
          return;
        } else {
          this.alreadyRefreshing = true;
        }
        await this.refreshFilesListInDriveAndStoreInSettings();
        this.cloudFiles = [];
        this.localFiles = [];
        this.settings.filesList.map((file2) => this.cloudFiles.push(file2.name));
        this.app.vault.getFiles().map((file2) => this.localFiles.push(file2.path));
        var toDownload = this.cloudFiles.filter((file2) => !this.localFiles.includes(file2) && !this.renamingList.includes(file2) && !this.deletingList.includes(file2) && !this.isInBlacklist(file2));
        await this.writeToVerboseLogFile("LOG: Deleting files in refreshAll");
        this.app.vault.getFiles().map(async (file2) => {
          if (!this.cloudFiles.includes(file2.path) && !this.renamingList.includes(file2.path) && !this.deletingList.includes(file2.path) && file2.path != this.currentlyUploading) {
            if (file2.extension != "md") {
              if (await this.isAttachmentSynced(file2.path)) {
                this.app.vault.trash(file2, false);
                let convertedSafeFilename = file2.path.replace(/\//g, ".");
                try {
                  await this.adapter.remove(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`);
                } catch (err) {
                  await this.writeToErrorLogFile(err);
                  await this.writeToVerboseLogFile(`LOG: Could not delete ${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`);
                }
                return;
              }
            }
            var content = await this.app.vault.read(file2);
            if (driveDataPattern.test(content)) {
              this.app.vault.trash(file2, false);
            }
          }
        });
        await this.writeToVerboseLogFile("LOG: Downloading missing files in refreshAll");
        if (toDownload.length) {
          new import_obsidian2.Notice("Downloading missing files", 2500);
          this.settings.refresh = true;
          for (const dFile of toDownload) {
            var id;
            this.settings.filesList.map((file2) => {
              if (file2.name == dFile) {
                id = file2.id;
              }
            });
            var file = await getFile(this.settings.accessToken, id);
            let isBinary = file[0].split(".")[file[0].split(".").length - 1] != "md";
            try {
              await this.app.vault.createBinary(file[0], file[1]);
              if (isBinary) {
                let safeFilename = file[0].replace(/\//g, ".");
                try {
                  await this.app.vault.create(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${safeFilename}`, "");
                } catch (err) {
                  await this.writeToVerboseLogFile(`LOG: ${ATTACHMENT_TRACKING_FOLDER_NAME}/${safeFilename} could not be created`);
                  await this.writeToErrorLogFile(err);
                }
              }
            } catch (err) {
              await this.writeToVerboseLogFile("LOG: Couldn't create file directly, trying to create folder first...");
              var path = file[0].split("/").slice(0, -1).join("/");
              try {
                await this.app.vault.createFolder(path);
              } catch (err2) {
                if (err2.message.includes("Folder already exists")) {
                  await this.writeToVerboseLogFile("LOG: Caught: Folder exists");
                }
              }
              try {
                await this.app.vault.createBinary(file[0], file[1]);
              } catch (err2) {
                await this.writeToVerboseLogFile("LOG: Couldn't create file and folder, details of path, file[0]: " + path + ", " + file[0]);
                await this.writeToErrorLogFile(err2);
                await this.notifyError();
              }
            }
            new import_obsidian2.Notice(`Downloaded ${toDownload.indexOf(dFile) + 1}/${toDownload.length} files`, 1e3);
          }
          new import_obsidian2.Notice("Download complete :)", 2500);
          this.settings.refresh = false;
        }
        if (!this.attachmentTrackingInitializationComplete) {
          console.log("Initializing attachment tracking...");
          for (const file2 of this.cloudFiles) {
            if (file2.slice(-3) == ".md") {
              continue;
            }
            console.log("Trying to attachment tracking file: " + file2);
            let convertedSafeFilename = file2.replace(/\//g, ".");
            try {
              await this.app.vault.create(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`, "");
            } catch (err) {
              if (err.message.includes("exist")) {
                await this.writeToVerboseLogFile("LOG: Already tracked: " + file2);
              } else {
                await this.writeToErrorLogFile(err);
                await this.writeToVerboseLogFile(`LOG: Could not create ${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`);
              }
            }
          }
          this.attachmentTrackingInitializationComplete = true;
        }
        this.getLatestContent(this.app.workspace.getActiveFile());
        this.alreadyRefreshing = false;
      } catch (err) {
        this.notifyError();
        this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
        this.alreadyRefreshing = false;
      }
      await this.writeToVerboseLogFile("LOG: Exited refreshAll");
    };
    this.uploadNewNotesFile = async (newFile) => {
      if (this.haltAllOperations) {
        return;
      }
      if (this.isInBlacklist(newFile)) {
        new import_obsidian2.Notice("File in blacklist. It will be uploaded but not be synced/tracked automatically by the plugin.");
      }
      try {
        await this.writeToVerboseLogFile("LOG: Entering uploadNewNotesFile");
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not uploading...");
          return;
        }
        if (newFile.extension != "md" || newFile.path == this.currentlyUploading)
          return;
        this.writingFile = true;
        this.currentlyUploading = newFile.path;
        new import_obsidian2.Notice("Uploading new file to Google Drive!");
        var content = await this.app.vault.read(newFile);
        var metaExists = metaPattern.test(content);
        var driveDataExists = driveDataPattern.test(content);
        if (!metaExists) {
          await this.app.vault.modify(newFile, `---
lastSync: ${new Date().toString()}
---
` + content);
        } else if (!driveDataExists) {
          await this.app.vault.modify(newFile, content.replace(/^---\n/g, `---
lastSync: ${new Date().toString()}
`));
        }
        var buffer = await this.app.vault.readBinary(newFile);
        var id = await uploadFile(this.settings.accessToken, newFile.path, buffer, this.settings.vaultId);
        this.writingFile = false;
        this.cloudFiles.push(newFile.path);
        await this.refreshFilesListInDriveAndStoreInSettings();
        this.currentlyUploading = null;
        new import_obsidian2.Notice("Uploaded!");
        await this.writeToVerboseLogFile("LOG: Exited uploadNewNotesFile");
        return id;
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
        this.writingFile = false;
        this.currentlyUploading = null;
        await this.writeToVerboseLogFile("LOG: Exited uploadNewNotesFile");
      }
    };
    this.getLatestContent = async (file, forced = false) => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      try {
        if (this.haltAllOperations) {
          return;
        }
        await this.writeToVerboseLogFile("LOG: Entering getLatestContent");
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not fetching latest content...");
          return;
        }
        if (this.cloudFiles.includes(file == null ? void 0 : file.path) && !this.syncQueue.length) {
          var index = this.cloudFiles.indexOf(file == null ? void 0 : file.path);
          var cloudDate = new Date(this.settings.filesList[index].modifiedTime);
          var content;
          var timeStamp;
          var isBinaryFile = false;
          if (file.extension != "md") {
            isBinaryFile = true;
            timeStamp = [file.stat.mtime];
          } else {
            content = await this.app.vault.cachedRead(file);
            timeStamp = content.match(/lastSync:.*/);
          }
          if (forced == "forced" || timeStamp && cloudDate.getTime() > new Date(timeStamp[0]).getTime() + (isBinaryFile ? 5e3 : 3e3)) {
            if (isBinaryFile && !parseInt(this.settings.autoRefreshBinaryFiles)) {
              return;
            }
            var id;
            this.settings.filesList.map((fileItem) => {
              if (fileItem.name == file.path) {
                id = fileItem.id;
              }
            });
            var res = await getFile(this.settings.accessToken, id);
            if (this.syncQueue.length || this.writingFile)
              return;
            this.latestContentThatWasSynced = res[1];
            await this.app.vault.modifyBinary(file, res[1]).catch(async () => {
              var path = res[0].split("/").slice(0, -1).join("/");
              await this.app.vault.createFolder(path);
              await this.app.vault.modifyBinary(res[0], res[1]);
            });
          }
        }
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
      await this.writeToVerboseLogFile("LOG: Exited getLatestContent");
    };
    this.emptySyncQueue = async () => {
      if (this.haltAllOperations) {
        return;
      }
      await this.writeToVerboseLogFile("LOG: Entering emptySyncQueue");
      let path = this.syncQueue.shift();
      this.isUploadingCurrentFile = true;
      let file = this.app.vault.getAbstractFileByPath(path);
      if (!(file instanceof import_obsidian2.TFile)) {
        return;
      }
      var id;
      this.settings.filesList.map((f) => {
        if (f.name == file.path) {
          id = f.id;
        }
      });
      if (file.extension == "md")
        await this.updateLastSyncMetaTag(file);
      var buffer = await this.app.vault.readBinary(file);
      await modifyFile(this.settings.accessToken, id, buffer);
      await this.refreshFilesListInDriveAndStoreInSettings();
      this.statusBarItem.classList.replace("sync_icon", "sync_icon_still");
      (0, import_obsidian2.setIcon)(this.statusBarItem, "checkmark");
      this.isUploadingCurrentFile = false;
      await this.writeToVerboseLogFile("LOG: Exited emptySyncQueue");
    };
    this.checkAndEmptySyncQueue = async () => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      if (this.haltAllOperations || this.completingPendingSync || !this.connectedToInternet)
        return;
      await this.writeToVerboseLogFile("LOG: Entering checkAndEmptySyncQueue");
      if (this.haltAllOperations) {
        return;
      }
      if (this.syncQueue.length && !this.isUploadingCurrentFile) {
        this.emptySyncQueue();
      }
    };
    this.uploadNewAttachment = async (e) => {
      if (this.haltAllOperations) {
        return;
      }
      if (this.isInBlacklist(e)) {
        new import_obsidian2.Notice("File is listed in blacklist. It will be uploaded but not be tracked by the plugin automatically.");
      }
      try {
        await this.writeToVerboseLogFile("LOG: Entering uploadNewAttachment");
        new import_obsidian2.Notice("Uploading new attachment!");
        var buffer = await this.app.vault.readBinary(e);
        this.currentlyUploading = e.path;
        this.cloudFiles.push(e.path);
        try {
          await this.app.vault.create(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${e.path.replace(/\//g, ".")}`, "");
        } catch (err) {
          await this.writeToErrorLogFile(err);
          await this.writeToVerboseLogFile(`LOG: Could not create attachment tracking file: ${ATTACHMENT_TRACKING_FOLDER_NAME}/${e.path.replace(/\//g, ".")}`);
        }
        let id = await uploadFile(this.settings.accessToken, e.path, buffer, this.settings.vaultId);
        this.currentlyUploading = null;
        new import_obsidian2.Notice("Uploaded!");
        return id;
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
      await this.writeToVerboseLogFile("LOG: Exited uploadNewAttachment");
    };
    this.updateLastSyncMetaTag = async (e) => {
      var _a, _b;
      await this.writeToVerboseLogFile("LOG: Entering updateLastSyncMetaTag");
      var content = await this.app.vault.read(e);
      var metaExists = metaPattern.test(content);
      var driveDataExists = driveDataPattern.test(content);
      const lastEditor = this.app.workspace.activeEditor;
      if (metaExists) {
        if (driveDataExists) {
          await this.app.vault.modify(e, content.replace(driveDataPattern, `
lastSync: ${new Date().toString()}
`));
        } else {
          await this.app.vault.modify(e, content.replace(/^---\n/g, `---
lastSync: ${new Date().toString()}
`));
        }
      } else {
        await this.app.vault.modify(e, `---
lastSync: ${new Date().toString()}
---
` + content);
      }
      if (this.settings.forceFocus && lastEditor && !((_a = lastEditor.editor) == null ? void 0 : _a.hasFocus())) {
        (_b = lastEditor == null ? void 0 : lastEditor.editor) == null ? void 0 : _b.focus();
      }
      await this.writeToVerboseLogFile("LOG: Exited updateLastSyncMetaTag");
    };
    this.writeToPendingSyncFile = async () => {
      await this.writeToVerboseLogFile("LOG: Entering writeToPendingSyncFile");
      let pendingSyncFile = this.app.vault.getAbstractFileByPath(PENDING_SYNC_FILE_NAME);
      if (pendingSyncFile instanceof import_obsidian2.TFile) {
        await this.app.vault.modify(pendingSyncFile, JSON.stringify({
          pendingSyncItems: this.pendingSyncItems,
          finalNamesForFileID: mapToObject(this.finalNamesForFileID)
        }));
      } else {
        try {
          await this.app.vault.create(PENDING_SYNC_FILE_NAME, JSON.stringify({
            pendingSyncItems: this.pendingSyncItems,
            finalNamesForFileID: mapToObject(this.finalNamesForFileID)
          }));
        } catch (err) {
          console.log("CAUGHT: ERROR for PENDIND SYNC: Probably during startup");
        }
      }
      await this.writeToVerboseLogFile("LOG: Exited writeToPendingSyncFile");
    };
    this.refreshFilesListInDriveAndStoreInSettings = async () => {
      if (this.haltAllOperations) {
        return;
      }
      await this.writeToVerboseLogFile("LOG: Entering refreshFilesListInDriveAndStoreInSettings");
      try {
        this.settings.filesList = await getFilesList(this.settings.accessToken, this.settings.vaultId);
      } catch (err) {
        this.notifyError();
        this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
      this.saveSettings();
      await this.writeToVerboseLogFile("LOG: Exiting refreshFilesListInDriveAndStoreInSettings");
    };
    this.writeToErrorLogFile = async (log) => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      await this.writeToVerboseLogFile("LOG: Entering writeToErrorLogFile");
      if (!this.settings.errorLoggingToFile) {
        return;
      }
      let errorLogFile = this.app.vault.getAbstractFileByPath(ERROR_LOG_FILE_NAME);
      console.log(log.stack, "logging");
      let content;
      try {
        if (errorLogFile instanceof import_obsidian2.TFile) {
          content = !this.errorLoggingForTheFirstTimeInThisSession ? await this.app.vault.read(errorLogFile) : "";
          await this.app.vault.modify(errorLogFile, `${content}

${new Date().toString()}-${log.name}-${log.message}-${log.stack}`);
          this.errorLoggingForTheFirstTimeInThisSession = false;
        } else {
          try {
            await this.app.vault.create(ERROR_LOG_FILE_NAME, `${new Date().toString()}-${log.name}-${log.message}-${log.stack}`);
          } catch (err) {
            console.log("CAUGHT: ERROR for ERROR LOGS: Probably during startup");
          }
        }
      } catch (err) {
        console.log(err);
      }
      await this.writeToVerboseLogFile("LOG: Exited writeToErrorLogFile");
    };
    this.writeToVerboseLogFile = async (log) => {
      if (!this.app.workspace.layoutReady || !this.layoutReady) {
        return;
      }
      if (!this.settings.verboseLoggingToFile) {
        return;
      }
      let verboseLogFile = this.app.vault.getAbstractFileByPath(VERBOSE_LOG_FILE_NAME);
      console.log(log);
      let content;
      try {
        if (verboseLogFile instanceof import_obsidian2.TFile) {
          content = !this.verboseLoggingForTheFirstTimeInThisSession ? await this.app.vault.read(verboseLogFile) : "";
          await this.app.vault.modify(verboseLogFile, `${content}

${log}`);
          this.verboseLoggingForTheFirstTimeInThisSession = false;
        } else {
          try {
            await this.app.vault.create(VERBOSE_LOG_FILE_NAME, `${log}`);
          } catch (err) {
            console.log("CAUGHT: ERROR for VERBOSE LOGS: Probably during startup");
          }
        }
      } catch (err) {
        console.log(err);
      }
    };
    this.isInBlacklist = (file) => {
      if (typeof file === "string") {
        for (const path of this.settings.blacklistPaths) {
          if (file.includes(path))
            return true;
        }
        return false;
      }
      for (const path of this.settings.blacklistPaths) {
        if (file.path.includes(path))
          return true;
      }
      return false;
    };
    this.isAttachmentSynced = async (filename) => {
      const attachmentsAlreadySynced = (await this.adapter.list(ATTACHMENT_TRACKING_FOLDER_NAME)).files;
      const convertedSafeFilename = filename.replace(/\//g, ".");
      for (const attachment of attachmentsAlreadySynced) {
        if (attachment.includes(convertedSafeFilename))
          return true;
      }
      return false;
    };
    this.initFunction = async () => {
      this.adapter = this.app.vault.adapter;
      this.layoutReady = true;
      await this.loadSettings();
      await this.writeToVerboseLogFile("LOG: getAccessToken");
      var res = await getAccessToken(this.settings.refreshToken, this.settings.refreshAccessTokenURL, true);
      var count = 0;
      while (res == "error") {
        new import_obsidian2.Notice("ERROR: Couldn't fetch accessToken. Trying again in 5 secs, please wait...");
        await this.writeToErrorLogFile(new Error("ERROR: Couldn't fetch accessToken. Trying again in 5 secs."));
        await this.writeToVerboseLogFile("LOG: failed to fetch accessToken");
        if (!this.settings.refreshToken) {
          await this.writeToVerboseLogFile("LOG: no refreshToken");
          break;
        }
        console.log("Trying to get accessToken again after 5secs...");
        let resolvePromise;
        let promise = new Promise((resolve, reject) => {
          resolvePromise = resolve;
        });
        setTimeout(() => {
          resolvePromise();
        }, 5e3);
        await promise;
        await this.writeToVerboseLogFile("LOG: trying to fetch accessToken again");
        res = await getAccessToken(this.settings.refreshToken, this.settings.refreshAccessTokenURL);
        count++;
        if (count == 6) {
          this.settings.accessToken = "";
          this.settings.validToken = false;
          new import_obsidian2.Notice("FATAL ERROR: Connection timeout, couldn't fetch accessToken :(");
          new import_obsidian2.Notice("Check your internet connection and restart the plugin...");
          this.connectedToInternet = false;
          this.settings.filesList.map((file) => this.cloudFiles.push(file.name));
          this.app.vault.getFiles().map((file) => this.localFiles.push(file.path));
          let pendingSyncFile = this.app.vault.getAbstractFileByPath(PENDING_SYNC_FILE_NAME);
          let {
            pendingSyncItems,
            finalNamesForFileID
          } = pendingSyncFile instanceof import_obsidian2.TFile ? JSON.parse(await this.app.vault.read(pendingSyncFile)) : {
            pendingSyncItems: [],
            finalNamesForFileID: /* @__PURE__ */ new Map()
          };
          this.pendingSyncItems = [...pendingSyncItems];
          this.finalNamesForFileID = objectToMap(finalNamesForFileID);
          break;
        }
      }
      if (res == "network_error" && this.settings.vaultId) {
        this.connectedToInternet = false;
        new import_obsidian2.Notice("Recording offline changes...");
        await this.writeToVerboseLogFile("NO CONNECTION: Swtiched to offline sync");
      }
      try {
        if (res != "error" && res != "network_error") {
          this.connectedToInternet = true;
          await this.writeToVerboseLogFile("LOG: received accessToken");
          this.settings.accessToken = res.access_token;
          this.settings.accessTokenExpiryTime = res.expiry_date;
          this.settings.validToken = true;
          var folders = await getFoldersList(this.settings.accessToken);
          var reqFolder = folders.filter((folder) => folder.name == "obsidian");
          if (reqFolder.length) {
            await this.writeToVerboseLogFile("LOG: rootFolder available");
            this.settings.rootFolderId = reqFolder[0].id;
          } else {
            await this.writeToVerboseLogFile("LOG: rootFolder unavailable, uploading");
            new import_obsidian2.Notice("Initializing required files");
            this.settings.rootFolderId = await uploadFolder(this.settings.accessToken, "obsidian");
          }
          this.saveSettings();
        }
      } catch (err) {
        await this.notifyError();
        await this.writeToVerboseLogFile("FATAL ERROR: Could not fetch rootFolder");
        await this.writeToErrorLogFile(err);
        await this.writeToErrorLogFile(new Error("FATAL ERROR: Could not fetch rootFolder"));
        new import_obsidian2.Notice("FATAL ERROR: Could not fetch rootFolder");
        await this.writeToVerboseLogFile("LOG: adding settings UI");
        this.addSettingTab(new syncSettings(this.app, this));
        return;
      }
      if (this.settings.validToken) {
        try {
          await this.writeToVerboseLogFile("LOG: getting vault id");
          this.settings.vaultId = await getVaultId(this.settings.accessToken, this.app.vault.getName(), this.settings.rootFolderId);
        } catch (err) {
          await this.writeToErrorLogFile(err);
          if (this.connectedToInternet && !this.settings.vaultId) {
            new import_obsidian2.Notice("FATAL ERROR: Couldn't get VaultID from Google Drive :(");
            await this.writeToVerboseLogFile("FATAL ERROR: Couldn't get VaultID from Google Drive :(");
          }
          new import_obsidian2.Notice("Check internet connection and restart plugin.");
          await this.writeToVerboseLogFile("LOG: adding settings UI");
          this.addSettingTab(new syncSettings(this.app, this));
        }
        if (this.settings.vaultId == "NOT FOUND") {
          await this.writeToVerboseLogFile("LOG: vault not found");
          this.settings.vaultInit = false;
          new import_obsidian2.Notice(`Oops! No vaults named ${this.app.vault.getName()} found in Google Drive`);
          new import_obsidian2.Notice("Try initializing vault in Google Drive from plug-in settings :)", 5e3);
        } else {
          this.settings.vaultInit = true;
          if (this.connectedToInternet) {
            await this.completeAllPendingSyncs();
          } else {
            this.checkForConnectivity();
          }
          try {
            await this.app.vault.createFolder(ATTACHMENT_TRACKING_FOLDER_NAME);
          } catch (err) {
            if (err.message.includes("exist")) {
              console.log("It's fine, folder exists.");
            } else {
              new import_obsidian2.Notice("FATAL ERROR: Could not create folder for tracking attachments!");
              await this.writeToErrorLogFile(err);
            }
          }
          this.refreshAll();
          this.registerInterval(window.setInterval(async () => {
            this.refreshAll();
          }, parseInt(this.settings.refreshTime) * 1e3));
          this.registerInterval(window.setInterval(async () => {
            this.checkAndEmptySyncQueue();
          }, 1e3));
        }
      } else {
        new import_obsidian2.Notice("ERROR: Invalid token");
        this.writeToErrorLogFile(new Error("ERROR: Invalid token"));
      }
      await this.writeToVerboseLogFile("LOG: adding settings UI");
      this.addSettingTab(new syncSettings(this.app, this));
      if (!this.settings.vaultInit)
        return;
      this.settings.filesList.map((file) => this.cloudFiles.push(file.name));
      this.app.vault.getFiles().map((file) => this.localFiles.push(file.path));
    };
  }
  async onload() {
    this.app.workspace.onLayoutReady(this.initFunction);
    this.registerEvent(this.app.vault.on("rename", async (newFile, oldpath) => {
      if (ignoreFiles.includes(newFile.path)) {
        return;
      }
      if (this.isInBlacklist(newFile)) {
        return;
      }
      if (this.completingPendingSync) {
        await this.writeToVerboseLogFile("LOG: not renaming as pending sync is ongoing");
        return;
      }
      try {
        if (!this.connectedToInternet) {
          await this.writeToVerboseLogFile("LOG: Connectivity lost, not renaming files to Google Drive");
          console.log("ERROR: Connectivity lost, not renaming files to Google Drive...");
          if (!this.cloudFiles.length) {
            console.log("FATAL ERROR: Nothing in cloudFiles....");
            return;
          }
          if (!this.cloudFiles.includes(oldpath) && !this.renamedWhileOffline.get(oldpath)) {
            await this.writeToVerboseLogFile("LOG: new file created while offline");
            if (newFile instanceof import_obsidian2.TFile) {
              let id2 = randomUUID();
              this.pendingSyncItems.push({
                newFileName: newFile.path,
                action: "UPLOAD",
                fileID: id2,
                timeStamp: new Date().toString()
              });
              this.renamedWhileOffline.set(newFile.path, id2);
              this.finalNamesForFileID.set(id2, newFile.path);
            }
          } else {
            await this.writeToVerboseLogFile("LOG: renamed while offline");
            let idIfWasAlreadyRenamedOffline = this.renamedWhileOffline.get(oldpath);
            let id2;
            if (idIfWasAlreadyRenamedOffline) {
              id2 = idIfWasAlreadyRenamedOffline;
            } else {
              this.settings.filesList.map((file, index) => {
                if (file.name == oldpath) {
                  id2 = file.id;
                }
              });
            }
            this.pendingSyncItems.push({
              fileID: id2,
              action: "RENAME",
              timeStamp: new Date().toString()
            });
            this.renamedWhileOffline.set(newFile.path, id2);
            this.renamedWhileOffline.delete(oldpath);
            this.finalNamesForFileID.set(id2, newFile.path);
            if (newFile instanceof import_obsidian2.TFile) {
              if (newFile.extension != "md") {
                try {
                  let oldSafeFilename = oldpath.replace(/\//g, ".");
                  let newSafeFilename = newFile.path.replace(/\//g, ".");
                  await this.adapter.remove(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${oldSafeFilename}`);
                  await this.app.vault.create(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${newSafeFilename}`, "");
                } catch (err) {
                  await this.writeToVerboseLogFile(`LOG: ${ATTACHMENT_TRACKING_FOLDER_NAME}/${oldpath.replace(/\//g, ".")} could not be renamed`);
                  await this.writeToErrorLogFile(err);
                }
              }
            }
          }
          await this.writeToPendingSyncFile();
          return;
        }
        if (!this.cloudFiles.includes(oldpath)) {
          if (newFile instanceof import_obsidian2.TFile) {
            this.uploadNewNotesFile(newFile);
          }
          return;
        }
        await this.writeToVerboseLogFile("LOG: renaming while online");
        var id;
        var reqFile = "";
        this.settings.filesList.map((file, index) => {
          if (file.name == oldpath) {
            id = file.id;
            reqFile = file.name;
          }
        });
        this.renamingList.push(oldpath);
        this.cloudFiles[this.cloudFiles.indexOf(reqFile)] = newFile.path;
        await renameFile(this.settings.accessToken, id, newFile.path);
        if (newFile instanceof import_obsidian2.TFile) {
          if (newFile.extension != "md") {
            try {
              let oldSafeFilename = oldpath.replace(/\//g, ".");
              let newSafeFilename = newFile.path.replace(/\//g, ".");
              await this.adapter.remove(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${oldSafeFilename}`);
              await this.app.vault.create(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${newSafeFilename}`, "");
            } catch (err) {
              await this.writeToVerboseLogFile(`LOG: ${ATTACHMENT_TRACKING_FOLDER_NAME}/${oldpath.replace(/\//g, ".")} could not be renamed`);
              await this.writeToErrorLogFile(err);
            }
          }
        }
        new import_obsidian2.Notice("Files/Folders renamed!");
        this.renamingList.splice(this.renamingList.indexOf(oldpath), 1);
        await this.writeToVerboseLogFile("LOG: renamed while online");
        await this.refreshFilesListInDriveAndStoreInSettings();
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
        this.renamingList = [];
      }
    }));
    this.registerEvent(this.app.vault.on("create", async (e) => {
      if (!this.app.workspace.layoutReady) {
        return;
      }
      if (ignoreFiles.includes(e.path)) {
        return;
      }
      if (this.isInBlacklist(e)) {
        return;
      }
      if (this.completingPendingSync) {
        await this.writeToVerboseLogFile("LOG: not uploading as pending sync is ongoing");
        return;
      }
      try {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not uploading files to Google Drive...");
          await this.writeToVerboseLogFile("LOG: Connectivity lost, not uploading files to Google Drive");
          if (e instanceof import_obsidian2.TFile && !this.cloudFiles.includes(e.path)) {
            if (e.extension != "md") {
              await this.writeToVerboseLogFile("LOG: created attachment while offline");
              let id = randomUUID();
              this.pendingSyncItems.push({
                action: "UPLOAD",
                timeStamp: new Date().toString(),
                newFileName: e.path,
                isBinaryFile: true,
                fileID: id
              });
              this.renamedWhileOffline.set(e.path, id);
              this.finalNamesForFileID.set(id, e.path);
            }
          }
          await this.writeToPendingSyncFile();
          return;
        }
        if (e instanceof import_obsidian2.TFile && !this.cloudFiles.includes(e.path)) {
          if (e.extension != "md") {
            await this.writeToVerboseLogFile("LOG: created attachment while online");
            await this.uploadNewAttachment(e);
          }
        }
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
        this.currentlyUploading = null;
      }
    }));
    this.registerEvent(this.app.vault.on("delete", async (e) => {
      if (ignoreFiles.includes(e.path)) {
        return;
      }
      if (this.completingPendingSync) {
        await this.writeToVerboseLogFile("LOG: not deleting as pending sync is ongoing");
        return;
      }
      if (e instanceof import_obsidian2.TFile && e.extension != "md") {
        let convertedSafeFilename = e.path.replace(/\//g, ".");
        try {
          await this.adapter.remove(`${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`);
        } catch (err) {
          await this.writeToErrorLogFile(err);
          await this.writeToVerboseLogFile(`LOG: Could not delete ${ATTACHMENT_TRACKING_FOLDER_NAME}/${convertedSafeFilename}`);
        }
      }
      try {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not deleting files from Google Drive...");
          await this.writeToVerboseLogFile("LOG: Connectivity lost, not deleting files from Google Drive");
          let id2;
          this.settings.filesList.map((file, index) => {
            if (file.name == e.path) {
              id2 = file.id;
            }
          });
          await this.writeToVerboseLogFile("LOG: deleting while offline");
          this.pendingSyncItems.push({
            fileID: id2,
            action: "DELETE",
            timeStamp: new Date().toString()
          });
          this.renamedWhileOffline.delete(e.path);
          if (id2)
            this.finalNamesForFileID.delete(id2);
          await this.writeToPendingSyncFile();
          return;
        }
        if (this.settings.refresh)
          return;
        var id;
        this.settings.filesList.map((file) => {
          if (file.name == e.path) {
            id = file.id;
          }
        });
        this.deletingList.push(e.path);
        await this.writeToVerboseLogFile("LOG: deleting while online");
        var successful = await deleteFile(this.settings.accessToken, id);
        if (successful)
          new import_obsidian2.Notice("File deleted!");
        this.deletingList.splice(this.deletingList.indexOf(e.path), 1);
        await this.refreshFilesListInDriveAndStoreInSettings();
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
        this.deletingList = [];
      }
    }));
    this.registerEvent(this.app.vault.on("modify", async (e) => {
      if (ignoreFiles.includes(e.path)) {
        return;
      }
      if (this.isInBlacklist(e)) {
        return;
      }
      if (this.completingPendingSync) {
        await this.writeToVerboseLogFile("LOG: not modifying because pending sync");
        return;
      }
      try {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not modifying files on Google Drive...");
          await this.writeToVerboseLogFile("LOG: Connectivity lost, not modifying files on Google Drive");
          if (!this.cloudFiles.length) {
            console.log("FATAL ERROR: Nothing in cloudFiles....");
            return;
          }
          if (!this.cloudFiles.includes(e.path) && !this.renamedWhileOffline.get(e.path)) {
            if (e instanceof import_obsidian2.TFile) {
              await this.writeToVerboseLogFile("LOG: created file while offline");
              let id = randomUUID();
              this.pendingSyncItems.push({
                newFileName: e.path,
                action: "UPLOAD",
                timeStamp: new Date().toString(),
                fileID: id
              });
              this.renamedWhileOffline.set(e.path, id);
              this.finalNamesForFileID.set(id, e.path);
            }
          } else {
            let id;
            if (this.renamedWhileOffline.get(e.path)) {
              id = this.renamedWhileOffline.get(e.path);
            } else {
              this.settings.filesList.map((file, index) => {
                if (file.name == e.path) {
                  id = file.id;
                }
              });
            }
            let lastItemOnPendingSync = this.pendingSyncItems[this.pendingSyncItems.length - 1];
            if ((lastItemOnPendingSync == null ? void 0 : lastItemOnPendingSync.fileID) == id && (lastItemOnPendingSync == null ? void 0 : lastItemOnPendingSync.action) == "MODIFY") {
              this.pendingSyncItems.pop();
            }
            await this.writeToVerboseLogFile("LOG: modifying file while offline");
            this.pendingSyncItems.push({
              fileID: id,
              action: "MODIFY",
              timeStamp: new Date().toString()
            });
            this.finalNamesForFileID.set(id, e.path);
          }
          await this.writeToPendingSyncFile();
          return;
        }
        if (!this.cloudFiles.includes(e.path)) {
          if (e instanceof import_obsidian2.TFile) {
            await this.writeToVerboseLogFile("LOG: created file while online");
            this.uploadNewNotesFile(e);
          }
          return;
        }
        this.writingFile = true;
        this.statusBarItem.classList.replace("sync_icon_still", "sync_icon");
        (0, import_obsidian2.setIcon)(this.statusBarItem, "sync");
        if (this.timer)
          clearTimeout(this.timer);
        this.timer = setTimeout(async () => {
          if (e instanceof import_obsidian2.TFile) {
            if (this.settings.removeMergeNotifsSettings) {
              let intervalId = setInterval(() => {
                removeMergeNotifs();
                setTimeout(() => {
                  clearInterval(intervalId);
                }, 2500);
              }, 100);
            }
            var buffer = await this.app.vault.readBinary(e);
            if (this.latestContentThatWasSynced != null && bufferEqual(buffer, this.latestContentThatWasSynced)) {
              console.log("ignoring modify trigger due to updation from getLatestContent");
              this.statusBarItem.classList.replace("sync_icon", "sync_icon_still");
              (0, import_obsidian2.setIcon)(this.statusBarItem, "checkmark");
              this.writingFile = false;
              return;
            }
            let content = await this.app.vault.cachedRead(e);
            let timeStamp = e.extension == "md" ? content.match(/lastSync:.*/) : false;
            if (timeStamp) {
              if (Math.abs(new Date(timeStamp[0]).getTime() - new Date(e.stat.mtime).getTime()) < 1e3) {
                console.log("ignoring modify trigger due to lastSyncTag updation");
                this.statusBarItem.classList.replace("sync_icon", "sync_icon_still");
                (0, import_obsidian2.setIcon)(this.statusBarItem, "checkmark");
                this.writingFile = false;
                return;
              }
            }
          }
          if (this.syncQueue.contains(e.path))
            return;
          else
            this.syncQueue.push(e.path);
          await this.writeToVerboseLogFile("LOG: modifying file while online");
          this.writingFile = false;
        }, 2500);
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
    }));
    this.registerEvent(this.app.workspace.on("file-open", async (file) => {
      if ((file == null ? void 0 : file.extension) == "md")
        this.getLatestContent(file);
    }));
    const uploadEl = this.addRibbonIcon("cloud", "Upload Current File", async () => {
      if (!this.connectedToInternet) {
        console.log("ERROR: Connectivity lost, not uploading files to Google Drive...");
        new import_obsidian2.Notice("ERROR: No connectivity!");
        return;
      }
      var file = this.app.workspace.getActiveFile();
      if (!this.cloudFiles.includes(file == null ? void 0 : file.path)) {
        new ConfirmUpload(this.app, async () => {
          if (file.extension != "md") {
            await this.uploadNewAttachment(file);
          } else {
            await this.uploadNewNotesFile(file);
          }
        }).open();
        return;
      }
      try {
        new import_obsidian2.Notice("Uploading the current file to Google Drive!");
        var buffer = await this.app.vault.readBinary(this.app.workspace.getActiveFile());
        var id;
        this.settings.filesList.map((file2) => {
          var _a;
          if (file2.name == ((_a = this.app.workspace.getActiveFile()) == null ? void 0 : _a.path)) {
            id = file2.id;
          }
        });
        var res = await modifyFile(this.settings.accessToken, id, buffer);
        new import_obsidian2.Notice("Uploaded!");
      } catch (err) {
        await this.notifyError();
        await this.checkForConnectivity();
        await this.writeToErrorLogFile(err);
      }
    });
    const downloadEl = this.addRibbonIcon("install", "Download Current File", async () => {
      if (!this.connectedToInternet) {
        console.log("ERROR: Connectivity lost, not fetching files from Google Drive...");
        new import_obsidian2.Notice("ERROR: No connectivity!");
        return;
      }
      var ufile = this.app.workspace.getActiveFile();
      if (!this.cloudFiles.includes(ufile == null ? void 0 : ufile.path)) {
        new import_obsidian2.Notice("This file doesn't exist on Google Drive. Please upload it first.");
        return;
      }
      new import_obsidian2.Notice("Downloading current file!");
      await this.getLatestContent(ufile, "forced");
      new import_obsidian2.Notice("Sync complete :)");
    });
    this.addCommand({
      id: "drive-upload-current",
      name: "Upload current file to Google Drive",
      callback: async () => {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not uploading files to Google Drive...");
          new import_obsidian2.Notice("ERROR: No connectivity!");
          return;
        }
        var file = this.app.workspace.getActiveFile();
        if (!this.cloudFiles.includes(file == null ? void 0 : file.path)) {
          new ConfirmUpload(this.app, async () => {
            if (file.extension != "md") {
              await this.uploadNewAttachment(file);
            } else {
              await this.uploadNewNotesFile(file);
            }
          }).open();
          return;
        }
        try {
          new import_obsidian2.Notice("Uploading the current file to Google Drive!");
          var buffer = await this.app.vault.readBinary(this.app.workspace.getActiveFile());
          var id;
          this.settings.filesList.map((file2) => {
            var _a;
            if (file2.name == ((_a = this.app.workspace.getActiveFile()) == null ? void 0 : _a.path)) {
              id = file2.id;
            }
          });
          var res = await modifyFile(this.settings.accessToken, id, buffer);
          new import_obsidian2.Notice("Uploaded!");
        } catch (err) {
          await this.notifyError();
          await this.checkForConnectivity();
          await this.writeToErrorLogFile(err);
        }
      }
    });
    this.addCommand({
      id: "drive-download-current",
      name: "Download current file from Google Drive",
      callback: async () => {
        if (!this.connectedToInternet) {
          console.log("ERROR: Connectivity lost, not fetching files from Google Drive...");
          new import_obsidian2.Notice("ERROR: No connectivity!");
          return;
        }
        var ufile = this.app.workspace.getActiveFile();
        if (!this.cloudFiles.includes(ufile == null ? void 0 : ufile.path)) {
          new import_obsidian2.Notice("This file doesn't exist on Google Drive. Please upload it first.");
          return;
        }
        new import_obsidian2.Notice("Downloading current file!");
        await this.getLatestContent(ufile, "forced");
        new import_obsidian2.Notice("Sync complete :)");
      }
    });
    this.addCommand({
      id: "toggle-force-sync",
      name: "Toggle force focus",
      callback: async () => {
        this.settings.forceFocus = !this.settings.forceFocus;
        await this.saveSettings();
        new import_obsidian2.Notice(`Force focus is now ${this.settings.forceFocus ? "enabled" : "disabled"}`);
      }
    });
  }
  onunload() {
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};
var syncSettings = class extends import_obsidian2.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    const head = containerEl.createEl("h1", {
      text: "Google Drive Sync",
      cls: "main"
    });
    const sync = containerEl.createEl("div", {
      cls: "container-gdrive-plugin"
    });
    if (this.plugin.settings.validToken) {
      const sync_text = sync.createEl("div", {
        text: "Logged in",
        cls: "sync_text"
      });
      const sync_icons = sync.createDiv({ cls: "sync_icon_still" });
      (0, import_obsidian2.setIcon)(sync_icons, "checkmark");
    } else {
      const sync_link = sync.createEl("a", {
        text: "Open this link to log in",
        cls: "sync_text"
      });
      sync_link.href = this.plugin.settings.fetchRefreshTokenURL;
    }
    new import_obsidian2.Setting(containerEl).setName("Enable Error logging").setDesc("Error logs will appear in a .md file").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.errorLoggingToFile);
      toggle.onChange((val) => {
        this.plugin.settings.errorLoggingToFile = val;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName("Enable Verbose logging").setDesc("Verbose logs will appear in a .md file").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.verboseLoggingToFile);
      toggle.onChange((val) => {
        this.plugin.settings.verboseLoggingToFile = val;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName("Set refresh token").setDesc("Enter the refresh token you got from the link provided").addText((text) => text.setPlaceholder("Enter token").setValue(this.plugin.settings.refreshToken).onChange(async (value) => {
      this.plugin.settings.refreshToken = value;
    })).addButton((button) => button.setIcon("checkmark").onClick(async () => {
      await this.plugin.saveSettings();
      sync.innerHTML = "";
      const sync_text = sync.createEl("div", {
        text: "Checking...",
        cls: "sync_text"
      });
      const sync_icons = sync.createDiv({ cls: "sync_icon" });
      (0, import_obsidian2.setIcon)(sync_icons, "sync");
      var res = await getAccessToken(this.plugin.settings.refreshToken, this.plugin.settings.refreshAccessTokenURL);
      if (res != "error") {
        this.plugin.settings.accessToken = res.access_token;
        this.plugin.settings.validToken = true;
        new import_obsidian2.Notice("Logged in successfully");
        sync.innerHTML = "";
        const sync_text2 = sync.createEl("div", {
          text: "Logged in",
          cls: "sync_text"
        });
        const sync_icons2 = sync.createDiv({
          cls: "sync_icon_still"
        });
        (0, import_obsidian2.setIcon)(sync_icons2, "checkmark");
        new import_obsidian2.Notice("Please reload the plug-in", 5e3);
      } else {
        this.plugin.settings.accessToken = "";
        this.plugin.settings.validToken = false;
        new import_obsidian2.Notice("Log in failed");
        sync.innerHTML = "";
        const sync_link = sync.createEl("a", {
          text: "Open this link to log in",
          cls: "sync_text"
        });
        sync_link.href = this.plugin.settings.fetchRefreshTokenURL;
      }
      this.plugin.saveSettings();
    }));
    if (!this.plugin.settings.validToken)
      return;
    if (!this.plugin.settings.vaultInit) {
      new import_obsidian2.Setting(containerEl).setName("Initialize vault").setDesc("Create vault and sync all files to Google Drive. DO NOT use this button if you are getting errors related to root folder!").addButton((button) => {
        button.setButtonText("Proceed");
        button.onClick(async () => await this.plugin.cleanInstall());
      });
      new import_obsidian2.Setting(containerEl).setName("Create Root Folder Forecfully").setDesc("Experimental: Use this only if you get an error related to root folder.").addButton((button) => {
        button.setButtonText("Proceed");
        button.onClick(async () => {
          this.plugin.settings.rootFolderId = await uploadFolder(this.plugin.settings.accessToken, "obsidian");
          new import_obsidian2.Notice("Root folder created, please reload the plugin.");
          this.plugin.saveSettings();
        });
      });
      return;
    }
    new import_obsidian2.Setting(containerEl).setName("Set refresh time").setDesc("Enter the time in seconds after which the plugin checks for changed content. [Reload required]").addText((text) => text.setPlaceholder("Enter time").setValue(this.plugin.settings.refreshTime).onChange(async (value) => {
      this.plugin.settings.refreshTime = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian2.Setting(containerEl).setName("Auto refresh binary files").setDesc("Experimental: Automatically fetch lastest binary files. Currently this plugin doesn't completely support binary file sync.").addDropdown((selector) => {
      selector.addOption("1", "Fetch");
      selector.addOption("0", "Don't fetch");
      selector.setValue(this.plugin.settings.autoRefreshBinaryFiles);
      selector.onChange((val) => {
        this.plugin.settings.autoRefreshBinaryFiles = val;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName("Blacklist paths").setDesc("Add names for folders and files which should not be tracked by the plugin separated by comma. Example: templateFolder,dailyTemplateNote,file1,folder1 . NOTE: If folder name(s) is(are) mentioned, all files and folders under the mentioned folder would also be ignored.").addTextArea((textArea) => {
      textArea.setValue(this.plugin.settings.blacklistPaths.join(",")).onChange((value) => {
        this.plugin.settings.blacklistPaths = value.split(",");
      });
    });
    new import_obsidian2.Setting(containerEl).setName("Force Focus Mode").setDesc("Experimental: Forcefully bring the note in focus after each sync. Solves #45 issue on Github, but also introduces #75 issue. TLDR: Useful while working with tables, etc. when you lose focus while editing. Keep disabled otherwise. You can quickly toggle this setting using the command 'Toggle Force Focus Mode' in the command palette.").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.forceFocus);
      toggle.onChange((val) => {
        this.plugin.settings.forceFocus = val;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian2.Setting(containerEl).setName("Remove merging changes notices automatically (NOT RECOMMENDED)").setDesc("This enables a hacky fix that removes any merging changes notice that sometimes appears while typing. It does not prevent the notice from appearing but rather removes it as soon as it appears.").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.removeMergeNotifsSettings);
      toggle.onChange((val) => {
        this.plugin.settings.removeMergeNotifsSettings = val;
        this.plugin.saveSettings();
      });
    });
  }
};
var ConfirmUpload = class extends import_obsidian2.Modal {
  constructor(app, onSubmit) {
    super(app);
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h2", { text: "Wait a sec!" });
    new import_obsidian2.Setting(contentEl).setName("Seems like this file is missing from Google Drive. Either it has been created recently or was deleted from your other devices. You can upload it to Google Drive or manually delete it :)");
    new import_obsidian2.Setting(contentEl).addButton((btn) => btn.setButtonText("Okay").onClick(() => {
      this.close();
    })).addButton((btn) => btn.setButtonText("Upload").setCta().onClick(() => {
      this.close();
      this.onSubmit();
    }));
  }
  onClose() {
    let { contentEl } = this;
    contentEl.empty();
  }
};
