{"isPlusUser": true, "plusLicenseKey": "DHvAHiCFWePJkhzRqAu1Ntpg6Dra9bGrlz8ICkPH5es", "openAIApiKey": "", "openAIOrgId": "", "huggingfaceApiKey": "", "cohereApiKey": "", "anthropicApiKey": "", "azureOpenAIApiKey": "", "azureOpenAIApiInstanceName": "", "azureOpenAIApiDeploymentName": "", "azureOpenAIApiVersion": "", "azureOpenAIApiEmbeddingDeploymentName": "", "googleApiKey": "", "openRouterAiApiKey": "", "defaultChainType": "copilot_plus", "defaultModelKey": "copilot-plus-flash|copilot-plus", "embeddingModelKey": "copilot-plus-large|copilot-plus-jina", "temperature": 0.25, "maxTokens": 16000, "contextTurns": 50, "userSystemPrompt": "", "openAIProxyBaseUrl": "", "openAIEmbeddingProxyBaseUrl": "", "stream": true, "defaultSaveFolder": "copilot-conversations", "defaultConversationTag": "copilot-conversation", "autosaveChat": true, "defaultOpenArea": "view", "customPromptsFolder": "copilot-custom-prompts", "indexVaultToVectorStore": "ON STARTUP", "qaExclusions": "", "qaInclusions": "", "chatNoteContextPath": "", "chatNoteContextTags": [], "enableIndexSync": true, "debug": true, "enableEncryption": false, "maxSourceChunks": 85, "groqApiKey": "", "mistralApiKey": "", "activeModels": [{"name": "copilot-plus-flash", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "core": true, "plusExclusive": true, "projectEnabled": false, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-flash-lite-preview-06-17", "provider": "openrouterai", "enabled": true, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-flash", "provider": "openrouterai", "enabled": true, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "google/gemini-2.5-pro", "provider": "openrouterai", "enabled": true, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-5", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-5-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-5-nano", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-4.1", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-4.1-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "projectEnabled": true, "capabilities": ["vision"]}, {"name": "gpt-4.1-nano", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "o4-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["reasoning"]}, {"name": "claude-3-5-sonnet-latest", "provider": "anthropic", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-4o", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "gpt-4o-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true, "capabilities": ["vision"]}, {"name": "o1-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "capabilities": ["reasoning"]}, {"name": "o3-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "capabilities": ["reasoning"]}, {"name": "claude-3-5-haiku-latest", "provider": "anthropic", "enabled": true, "isBuiltIn": true}, {"name": "command-r", "provider": "cohereai", "enabled": true, "isBuiltIn": true}, {"name": "command-r-plus", "provider": "cohereai", "enabled": true, "isBuiltIn": true}, {"name": "gemini-2.0-pro-exp", "provider": "google", "enabled": true, "isBuiltIn": true, "capabilities": ["reasoning", "vision", "websearch"], "apiKey": "AIzaSyDzgPoViAu8T7sTkkfuIYk8Df0tArEgnsQ", "baseUrl": "https://generativelanguage.googleapis.com/"}, {"name": "gemini-2.0-flash", "provider": "google", "enabled": true, "isBuiltIn": true, "capabilities": ["vision"]}, {"name": "azure-openai", "provider": "azure openai", "enabled": true, "isBuiltIn": true}, {"name": "grok-3-fast", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api.x.ai/v1", "apiKey": "************************************************************************************", "isEmbeddingModel": false, "capabilities": ["reasoning"], "stream": true, "displayName": "grok-3-fast", "enableCors": true}, {"name": "gemini-2.5-pro", "provider": "google", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api-proxy.me/gemini", "apiKey": "AIzaSyDzgPoViAu8T7sTkkfuIYk8Df0tArEgnsQ", "isEmbeddingModel": false, "capabilities": ["reasoning", "vision"], "stream": true}, {"name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api.gmi-serving.com/v1", "apiKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjdiMjNjNzIyLTE1NTMtNDgzNi04NDY4LTIyZjU2MjYwOWI2OCIsInR5cGUiOiJpZV9tb2RlbCJ9.GXg3njvRZAGTekFzoKSc0La6u-QVjvXNbL8QYhyHaFk", "isEmbeddingModel": false, "capabilities": ["reasoning"], "stream": true, "displayName": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON><PERSON>-14B", "enableCors": true}, {"name": "deno-gemini-pro", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://lgd-gemini.deno.dev/", "apiKey": "AIzaSyDzgPoViAu8T7sTkkfuIYk8Df0tArEgnsQ", "isEmbeddingModel": false, "capabilities": ["reasoning"], "stream": true, "displayName": "deno-gemini-pro", "enableCors": true}, {"name": "gemini-2.5-pro", "provider": "3rd party (openai-format)", "enabled": true, "isBuiltIn": false, "baseUrl": "https://tbai.xin/v1", "apiKey": "sk-8l0BW6BkKFbHyDtXEdixvCHVxJOJKEI2NtWg4IfaGUmyov2U", "isEmbeddingModel": false, "capabilities": ["reasoning"], "stream": true, "displayName": "T-gemini-2.5-pro", "enableCors": true}], "activeEmbeddingModels": [{"name": "copilot-plus-small", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true}, {"name": "copilot-plus-large", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "believerExclusive": true, "dimensions": 1024}, {"name": "copilot-plus-multilingual", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true, "plusExclusive": true, "dimensions": 512}, {"name": "text-embedding-3-small", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "text-embedding-3-large", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "embed-multilingual-light-v3.0", "provider": "cohereai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "text-embedding-004", "provider": "google", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}], "embeddingRequestsPerMin": 140, "embeddingBatchSize": 11, "disableIndexOnMobile": true, "showSuggestedPrompts": true, "showRelevantNotes": true, "numPartitions": 1, "enabledCommands": {}, "promptUsageTimestamps": {}, "defaultConversationNoteName": "{$topic}@{$date}_{$time}", "userId": "9b19bca6-1aad-478d-a158-09966989d56e", "passMarkdownImages": true, "enableCustomPromptTemplating": true, "lastDismissedVersion": "2.9.5", "includeActiveNoteAsContext": true, "allowAdditionalContext": true, "enableWordCompletion": false, "promptEnhancementTemplate": ""}