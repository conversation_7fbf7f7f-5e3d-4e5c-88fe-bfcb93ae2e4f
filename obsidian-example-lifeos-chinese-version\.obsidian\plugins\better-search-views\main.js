/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/solid-js/dist/solid.cjs
var require_solid = __commonJS({
  "node_modules/solid-js/dist/solid.cjs"(exports) {
    "use strict";
    var taskIdCounter = 1;
    var isCallbackScheduled = false;
    var isPerformingWork = false;
    var taskQueue = [];
    var currentTask = null;
    var shouldYieldToHost = null;
    var yieldInterval = 5;
    var deadline = 0;
    var maxYieldInterval = 300;
    var scheduleCallback = null;
    var scheduledCallback = null;
    var maxSigned31BitInt = 1073741823;
    function setupScheduler() {
      const channel = new MessageChannel(), port = channel.port2;
      scheduleCallback = () => port.postMessage(null);
      channel.port1.onmessage = () => {
        if (scheduledCallback !== null) {
          const currentTime = performance.now();
          deadline = currentTime + yieldInterval;
          const hasTimeRemaining = true;
          try {
            const hasMoreWork = scheduledCallback(hasTimeRemaining, currentTime);
            if (!hasMoreWork) {
              scheduledCallback = null;
            } else
              port.postMessage(null);
          } catch (error) {
            port.postMessage(null);
            throw error;
          }
        }
      };
      if (navigator && navigator.scheduling && navigator.scheduling.isInputPending) {
        const scheduling = navigator.scheduling;
        shouldYieldToHost = () => {
          const currentTime = performance.now();
          if (currentTime >= deadline) {
            if (scheduling.isInputPending()) {
              return true;
            }
            return currentTime >= maxYieldInterval;
          } else {
            return false;
          }
        };
      } else {
        shouldYieldToHost = () => performance.now() >= deadline;
      }
    }
    function enqueue(taskQueue2, task) {
      function findIndex() {
        let m = 0;
        let n = taskQueue2.length - 1;
        while (m <= n) {
          const k = n + m >> 1;
          const cmp = task.expirationTime - taskQueue2[k].expirationTime;
          if (cmp > 0)
            m = k + 1;
          else if (cmp < 0)
            n = k - 1;
          else
            return k;
        }
        return m;
      }
      taskQueue2.splice(findIndex(), 0, task);
    }
    function requestCallback(fn, options) {
      if (!scheduleCallback)
        setupScheduler();
      let startTime = performance.now(), timeout = maxSigned31BitInt;
      if (options && options.timeout)
        timeout = options.timeout;
      const newTask = {
        id: taskIdCounter++,
        fn,
        startTime,
        expirationTime: startTime + timeout
      };
      enqueue(taskQueue, newTask);
      if (!isCallbackScheduled && !isPerformingWork) {
        isCallbackScheduled = true;
        scheduledCallback = flushWork;
        scheduleCallback();
      }
      return newTask;
    }
    function cancelCallback(task) {
      task.fn = null;
    }
    function flushWork(hasTimeRemaining, initialTime) {
      isCallbackScheduled = false;
      isPerformingWork = true;
      try {
        return workLoop(hasTimeRemaining, initialTime);
      } finally {
        currentTask = null;
        isPerformingWork = false;
      }
    }
    function workLoop(hasTimeRemaining, initialTime) {
      let currentTime = initialTime;
      currentTask = taskQueue[0] || null;
      while (currentTask !== null) {
        if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {
          break;
        }
        const callback = currentTask.fn;
        if (callback !== null) {
          currentTask.fn = null;
          const didUserCallbackTimeout = currentTask.expirationTime <= currentTime;
          callback(didUserCallbackTimeout);
          currentTime = performance.now();
          if (currentTask === taskQueue[0]) {
            taskQueue.shift();
          }
        } else
          taskQueue.shift();
        currentTask = taskQueue[0] || null;
      }
      return currentTask !== null;
    }
    var sharedConfig = {
      context: void 0,
      registry: void 0
    };
    function setHydrateContext(context) {
      sharedConfig.context = context;
    }
    function nextHydrateContext() {
      return {
        ...sharedConfig.context,
        id: `${sharedConfig.context.id}${sharedConfig.context.count++}-`,
        count: 0
      };
    }
    var equalFn = (a, b) => a === b;
    var $PROXY = Symbol("solid-proxy");
    var $TRACK = Symbol("solid-track");
    var $DEVCOMP = Symbol("solid-dev-component");
    var signalOptions = {
      equals: equalFn
    };
    var ERROR = null;
    var runEffects = runQueue;
    var STALE = 1;
    var PENDING = 2;
    var UNOWNED = {
      owned: null,
      cleanups: null,
      context: null,
      owner: null
    };
    var NO_INIT = {};
    var Owner = null;
    var Transition = null;
    var Scheduler = null;
    var ExternalSourceFactory = null;
    var Listener = null;
    var Updates = null;
    var Effects = null;
    var ExecCount = 0;
    var [transPending, setTransPending] = /* @__PURE__ */ createSignal(false);
    function createRoot(fn, detachedOwner) {
      const listener = Listener, owner = Owner, unowned = fn.length === 0, root = unowned ? UNOWNED : {
        owned: null,
        cleanups: null,
        context: null,
        owner: detachedOwner === void 0 ? owner : detachedOwner
      }, updateFn = unowned ? fn : () => fn(() => untrack(() => cleanNode(root)));
      Owner = root;
      Listener = null;
      try {
        return runUpdates(updateFn, true);
      } finally {
        Listener = listener;
        Owner = owner;
      }
    }
    function createSignal(value, options) {
      options = options ? Object.assign({}, signalOptions, options) : signalOptions;
      const s = {
        value,
        observers: null,
        observerSlots: null,
        comparator: options.equals || void 0
      };
      const setter = (value2) => {
        if (typeof value2 === "function") {
          if (Transition && Transition.running && Transition.sources.has(s))
            value2 = value2(s.tValue);
          else
            value2 = value2(s.value);
        }
        return writeSignal(s, value2);
      };
      return [readSignal.bind(s), setter];
    }
    function createComputed(fn, value, options) {
      const c = createComputation(fn, value, true, STALE);
      if (Scheduler && Transition && Transition.running)
        Updates.push(c);
      else
        updateComputation(c);
    }
    function createRenderEffect(fn, value, options) {
      const c = createComputation(fn, value, false, STALE);
      if (Scheduler && Transition && Transition.running)
        Updates.push(c);
      else
        updateComputation(c);
    }
    function createEffect(fn, value, options) {
      runEffects = runUserEffects;
      const c = createComputation(fn, value, false, STALE), s = SuspenseContext && lookup(Owner, SuspenseContext.id);
      if (s)
        c.suspense = s;
      if (!options || !options.render)
        c.user = true;
      Effects ? Effects.push(c) : updateComputation(c);
    }
    function createReaction(onInvalidate, options) {
      let fn;
      const c = createComputation(() => {
        fn ? fn() : untrack(onInvalidate);
        fn = void 0;
      }, void 0, false, 0), s = SuspenseContext && lookup(Owner, SuspenseContext.id);
      if (s)
        c.suspense = s;
      c.user = true;
      return (tracking) => {
        fn = tracking;
        updateComputation(c);
      };
    }
    function createMemo(fn, value, options) {
      options = options ? Object.assign({}, signalOptions, options) : signalOptions;
      const c = createComputation(fn, value, true, 0);
      c.observers = null;
      c.observerSlots = null;
      c.comparator = options.equals || void 0;
      if (Scheduler && Transition && Transition.running) {
        c.tState = STALE;
        Updates.push(c);
      } else
        updateComputation(c);
      return readSignal.bind(c);
    }
    function createResource(pSource, pFetcher, pOptions) {
      let source;
      let fetcher;
      let options;
      if (arguments.length === 2 && typeof pFetcher === "object" || arguments.length === 1) {
        source = true;
        fetcher = pSource;
        options = pFetcher || {};
      } else {
        source = pSource;
        fetcher = pFetcher;
        options = pOptions || {};
      }
      let pr = null, initP = NO_INIT, id = null, loadedUnderTransition = false, scheduled = false, resolved = "initialValue" in options, dynamic = typeof source === "function" && createMemo(source);
      const contexts = /* @__PURE__ */ new Set(), [value, setValue] = (options.storage || createSignal)(options.initialValue), [error, setError] = createSignal(void 0), [track, trigger] = createSignal(void 0, {
        equals: false
      }), [state, setState] = createSignal(resolved ? "ready" : "unresolved");
      if (sharedConfig.context) {
        id = `${sharedConfig.context.id}${sharedConfig.context.count++}`;
        let v;
        if (options.ssrLoadFrom === "initial")
          initP = options.initialValue;
        else if (sharedConfig.load && (v = sharedConfig.load(id)))
          initP = v[0];
      }
      function loadEnd(p, v, error2, key) {
        if (pr === p) {
          pr = null;
          key !== void 0 && (resolved = true);
          if ((p === initP || v === initP) && options.onHydrated)
            queueMicrotask(() => options.onHydrated(key, {
              value: v
            }));
          initP = NO_INIT;
          if (Transition && p && loadedUnderTransition) {
            Transition.promises.delete(p);
            loadedUnderTransition = false;
            runUpdates(() => {
              Transition.running = true;
              completeLoad(v, error2);
            }, false);
          } else
            completeLoad(v, error2);
        }
        return v;
      }
      function completeLoad(v, err) {
        runUpdates(() => {
          if (err === void 0)
            setValue(() => v);
          setState(err !== void 0 ? "errored" : resolved ? "ready" : "unresolved");
          setError(err);
          for (const c of contexts.keys())
            c.decrement();
          contexts.clear();
        }, false);
      }
      function read() {
        const c = SuspenseContext && lookup(Owner, SuspenseContext.id), v = value(), err = error();
        if (err !== void 0 && !pr)
          throw err;
        if (Listener && !Listener.user && c) {
          createComputed(() => {
            track();
            if (pr) {
              if (c.resolved && Transition && loadedUnderTransition)
                Transition.promises.add(pr);
              else if (!contexts.has(c)) {
                c.increment();
                contexts.add(c);
              }
            }
          });
        }
        return v;
      }
      function load(refetching = true) {
        if (refetching !== false && scheduled)
          return;
        scheduled = false;
        const lookup2 = dynamic ? dynamic() : source;
        loadedUnderTransition = Transition && Transition.running;
        if (lookup2 == null || lookup2 === false) {
          loadEnd(pr, untrack(value));
          return;
        }
        if (Transition && pr)
          Transition.promises.delete(pr);
        const p = initP !== NO_INIT ? initP : untrack(() => fetcher(lookup2, {
          value: value(),
          refetching
        }));
        if (typeof p !== "object" || !(p && "then" in p)) {
          loadEnd(pr, p, void 0, lookup2);
          return p;
        }
        pr = p;
        scheduled = true;
        queueMicrotask(() => scheduled = false);
        runUpdates(() => {
          setState(resolved ? "refreshing" : "pending");
          trigger();
        }, false);
        return p.then((v) => loadEnd(p, v, void 0, lookup2), (e) => loadEnd(p, void 0, castError(e), lookup2));
      }
      Object.defineProperties(read, {
        state: {
          get: () => state()
        },
        error: {
          get: () => error()
        },
        loading: {
          get() {
            const s = state();
            return s === "pending" || s === "refreshing";
          }
        },
        latest: {
          get() {
            if (!resolved)
              return read();
            const err = error();
            if (err && !pr)
              throw err;
            return value();
          }
        }
      });
      if (dynamic)
        createComputed(() => load(false));
      else
        load(false);
      return [read, {
        refetch: load,
        mutate: setValue
      }];
    }
    function createDeferred(source, options) {
      let t, timeout = options ? options.timeoutMs : void 0;
      const node = createComputation(() => {
        if (!t || !t.fn)
          t = requestCallback(() => setDeferred(() => node.value), timeout !== void 0 ? {
            timeout
          } : void 0);
        return source();
      }, void 0, true);
      const [deferred, setDeferred] = createSignal(node.value, options);
      updateComputation(node);
      setDeferred(() => node.value);
      return deferred;
    }
    function createSelector(source, fn = equalFn, options) {
      const subs = /* @__PURE__ */ new Map();
      const node = createComputation((p) => {
        const v = source();
        for (const [key, val] of subs.entries())
          if (fn(key, v) !== fn(key, p)) {
            for (const c of val.values()) {
              c.state = STALE;
              if (c.pure)
                Updates.push(c);
              else
                Effects.push(c);
            }
          }
        return v;
      }, void 0, true, STALE);
      updateComputation(node);
      return (key) => {
        const listener = Listener;
        if (listener) {
          let l;
          if (l = subs.get(key))
            l.add(listener);
          else
            subs.set(key, l = /* @__PURE__ */ new Set([listener]));
          onCleanup(() => {
            l.delete(listener);
            !l.size && subs.delete(key);
          });
        }
        return fn(key, Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value);
      };
    }
    function batch(fn) {
      return runUpdates(fn, false);
    }
    function untrack(fn) {
      if (Listener === null)
        return fn();
      const listener = Listener;
      Listener = null;
      try {
        return fn();
      } finally {
        Listener = listener;
      }
    }
    function on(deps, fn, options) {
      const isArray = Array.isArray(deps);
      let prevInput;
      let defer = options && options.defer;
      return (prevValue) => {
        let input;
        if (isArray) {
          input = Array(deps.length);
          for (let i = 0; i < deps.length; i++)
            input[i] = deps[i]();
        } else
          input = deps();
        if (defer) {
          defer = false;
          return void 0;
        }
        const result = untrack(() => fn(input, prevInput, prevValue));
        prevInput = input;
        return result;
      };
    }
    function onMount(fn) {
      createEffect(() => untrack(fn));
    }
    function onCleanup(fn) {
      if (Owner === null)
        ;
      else if (Owner.cleanups === null)
        Owner.cleanups = [fn];
      else
        Owner.cleanups.push(fn);
      return fn;
    }
    function catchError(fn, handler) {
      ERROR || (ERROR = Symbol("error"));
      Owner = createComputation(void 0, void 0, true);
      Owner.context = {
        [ERROR]: [handler]
      };
      if (Transition && Transition.running)
        Transition.sources.add(Owner);
      try {
        return fn();
      } catch (err) {
        handleError(err);
      } finally {
        Owner = Owner.owner;
      }
    }
    function onError(fn) {
      ERROR || (ERROR = Symbol("error"));
      if (Owner === null)
        ;
      else if (Owner.context === null)
        Owner.context = {
          [ERROR]: [fn]
        };
      else if (!Owner.context[ERROR])
        Owner.context[ERROR] = [fn];
      else
        Owner.context[ERROR].push(fn);
    }
    function getListener() {
      return Listener;
    }
    function getOwner() {
      return Owner;
    }
    function runWithOwner(o, fn) {
      const prev = Owner;
      const prevListener = Listener;
      Owner = o;
      Listener = null;
      try {
        return runUpdates(fn, true);
      } catch (err) {
        handleError(err);
      } finally {
        Owner = prev;
        Listener = prevListener;
      }
    }
    function enableScheduling(scheduler = requestCallback) {
      Scheduler = scheduler;
    }
    function startTransition(fn) {
      if (Transition && Transition.running) {
        fn();
        return Transition.done;
      }
      const l = Listener;
      const o = Owner;
      return Promise.resolve().then(() => {
        Listener = l;
        Owner = o;
        let t;
        if (Scheduler || SuspenseContext) {
          t = Transition || (Transition = {
            sources: /* @__PURE__ */ new Set(),
            effects: [],
            promises: /* @__PURE__ */ new Set(),
            disposed: /* @__PURE__ */ new Set(),
            queue: /* @__PURE__ */ new Set(),
            running: true
          });
          t.done || (t.done = new Promise((res) => t.resolve = res));
          t.running = true;
        }
        runUpdates(fn, false);
        Listener = Owner = null;
        return t ? t.done : void 0;
      });
    }
    function useTransition() {
      return [transPending, startTransition];
    }
    function resumeEffects(e) {
      Effects.push.apply(Effects, e);
      e.length = 0;
    }
    function createContext(defaultValue, options) {
      const id = Symbol("context");
      return {
        id,
        Provider: createProvider(id),
        defaultValue
      };
    }
    function useContext(context) {
      let ctx;
      return (ctx = lookup(Owner, context.id)) !== void 0 ? ctx : context.defaultValue;
    }
    function children(fn) {
      const children2 = createMemo(fn);
      const memo = createMemo(() => resolveChildren(children2()));
      memo.toArray = () => {
        const c = memo();
        return Array.isArray(c) ? c : c != null ? [c] : [];
      };
      return memo;
    }
    var SuspenseContext;
    function getSuspenseContext() {
      return SuspenseContext || (SuspenseContext = createContext({}));
    }
    function enableExternalSource(factory) {
      if (ExternalSourceFactory) {
        const oldFactory = ExternalSourceFactory;
        ExternalSourceFactory = (fn, trigger) => {
          const oldSource = oldFactory(fn, trigger);
          const source = factory((x) => oldSource.track(x), trigger);
          return {
            track: (x) => source.track(x),
            dispose() {
              source.dispose();
              oldSource.dispose();
            }
          };
        };
      } else {
        ExternalSourceFactory = factory;
      }
    }
    function readSignal() {
      const runningTransition = Transition && Transition.running;
      if (this.sources && (runningTransition ? this.tState : this.state)) {
        if ((runningTransition ? this.tState : this.state) === STALE)
          updateComputation(this);
        else {
          const updates = Updates;
          Updates = null;
          runUpdates(() => lookUpstream(this), false);
          Updates = updates;
        }
      }
      if (Listener) {
        const sSlot = this.observers ? this.observers.length : 0;
        if (!Listener.sources) {
          Listener.sources = [this];
          Listener.sourceSlots = [sSlot];
        } else {
          Listener.sources.push(this);
          Listener.sourceSlots.push(sSlot);
        }
        if (!this.observers) {
          this.observers = [Listener];
          this.observerSlots = [Listener.sources.length - 1];
        } else {
          this.observers.push(Listener);
          this.observerSlots.push(Listener.sources.length - 1);
        }
      }
      if (runningTransition && Transition.sources.has(this))
        return this.tValue;
      return this.value;
    }
    function writeSignal(node, value, isComp) {
      let current = Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;
      if (!node.comparator || !node.comparator(current, value)) {
        if (Transition) {
          const TransitionRunning = Transition.running;
          if (TransitionRunning || !isComp && Transition.sources.has(node)) {
            Transition.sources.add(node);
            node.tValue = value;
          }
          if (!TransitionRunning)
            node.value = value;
        } else
          node.value = value;
        if (node.observers && node.observers.length) {
          runUpdates(() => {
            for (let i = 0; i < node.observers.length; i += 1) {
              const o = node.observers[i];
              const TransitionRunning = Transition && Transition.running;
              if (TransitionRunning && Transition.disposed.has(o))
                continue;
              if (TransitionRunning ? !o.tState : !o.state) {
                if (o.pure)
                  Updates.push(o);
                else
                  Effects.push(o);
                if (o.observers)
                  markDownstream(o);
              }
              if (!TransitionRunning)
                o.state = STALE;
              else
                o.tState = STALE;
            }
            if (Updates.length > 1e6) {
              Updates = [];
              if (false)
                ;
              throw new Error();
            }
          }, false);
        }
      }
      return value;
    }
    function updateComputation(node) {
      if (!node.fn)
        return;
      cleanNode(node);
      const owner = Owner, listener = Listener, time = ExecCount;
      Listener = Owner = node;
      runComputation(node, Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value, time);
      if (Transition && !Transition.running && Transition.sources.has(node)) {
        queueMicrotask(() => {
          runUpdates(() => {
            Transition && (Transition.running = true);
            Listener = Owner = node;
            runComputation(node, node.tValue, time);
            Listener = Owner = null;
          }, false);
        });
      }
      Listener = listener;
      Owner = owner;
    }
    function runComputation(node, value, time) {
      let nextValue;
      try {
        nextValue = node.fn(value);
      } catch (err) {
        if (node.pure) {
          if (Transition && Transition.running) {
            node.tState = STALE;
            node.tOwned && node.tOwned.forEach(cleanNode);
            node.tOwned = void 0;
          } else {
            node.state = STALE;
            node.owned && node.owned.forEach(cleanNode);
            node.owned = null;
          }
        }
        node.updatedAt = time + 1;
        return handleError(err);
      }
      if (!node.updatedAt || node.updatedAt <= time) {
        if (node.updatedAt != null && "observers" in node) {
          writeSignal(node, nextValue, true);
        } else if (Transition && Transition.running && node.pure) {
          Transition.sources.add(node);
          node.tValue = nextValue;
        } else
          node.value = nextValue;
        node.updatedAt = time;
      }
    }
    function createComputation(fn, init, pure, state = STALE, options) {
      const c = {
        fn,
        state,
        updatedAt: null,
        owned: null,
        sources: null,
        sourceSlots: null,
        cleanups: null,
        value: init,
        owner: Owner,
        context: null,
        pure
      };
      if (Transition && Transition.running) {
        c.state = 0;
        c.tState = state;
      }
      if (Owner === null)
        ;
      else if (Owner !== UNOWNED) {
        if (Transition && Transition.running && Owner.pure) {
          if (!Owner.tOwned)
            Owner.tOwned = [c];
          else
            Owner.tOwned.push(c);
        } else {
          if (!Owner.owned)
            Owner.owned = [c];
          else
            Owner.owned.push(c);
        }
      }
      if (ExternalSourceFactory) {
        const [track, trigger] = createSignal(void 0, {
          equals: false
        });
        const ordinary = ExternalSourceFactory(c.fn, trigger);
        onCleanup(() => ordinary.dispose());
        const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());
        const inTransition = ExternalSourceFactory(c.fn, triggerInTransition);
        c.fn = (x) => {
          track();
          return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);
        };
      }
      return c;
    }
    function runTop(node) {
      const runningTransition = Transition && Transition.running;
      if ((runningTransition ? node.tState : node.state) === 0)
        return;
      if ((runningTransition ? node.tState : node.state) === PENDING)
        return lookUpstream(node);
      if (node.suspense && untrack(node.suspense.inFallback))
        return node.suspense.effects.push(node);
      const ancestors = [node];
      while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {
        if (runningTransition && Transition.disposed.has(node))
          return;
        if (runningTransition ? node.tState : node.state)
          ancestors.push(node);
      }
      for (let i = ancestors.length - 1; i >= 0; i--) {
        node = ancestors[i];
        if (runningTransition) {
          let top = node, prev = ancestors[i + 1];
          while ((top = top.owner) && top !== prev) {
            if (Transition.disposed.has(top))
              return;
          }
        }
        if ((runningTransition ? node.tState : node.state) === STALE) {
          updateComputation(node);
        } else if ((runningTransition ? node.tState : node.state) === PENDING) {
          const updates = Updates;
          Updates = null;
          runUpdates(() => lookUpstream(node, ancestors[0]), false);
          Updates = updates;
        }
      }
    }
    function runUpdates(fn, init) {
      if (Updates)
        return fn();
      let wait = false;
      if (!init)
        Updates = [];
      if (Effects)
        wait = true;
      else
        Effects = [];
      ExecCount++;
      try {
        const res = fn();
        completeUpdates(wait);
        return res;
      } catch (err) {
        if (!wait)
          Effects = null;
        Updates = null;
        handleError(err);
      }
    }
    function completeUpdates(wait) {
      if (Updates) {
        if (Scheduler && Transition && Transition.running)
          scheduleQueue(Updates);
        else
          runQueue(Updates);
        Updates = null;
      }
      if (wait)
        return;
      let res;
      if (Transition) {
        if (!Transition.promises.size && !Transition.queue.size) {
          const sources = Transition.sources;
          const disposed = Transition.disposed;
          Effects.push.apply(Effects, Transition.effects);
          res = Transition.resolve;
          for (const e2 of Effects) {
            "tState" in e2 && (e2.state = e2.tState);
            delete e2.tState;
          }
          Transition = null;
          runUpdates(() => {
            for (const d of disposed)
              cleanNode(d);
            for (const v of sources) {
              v.value = v.tValue;
              if (v.owned) {
                for (let i = 0, len = v.owned.length; i < len; i++)
                  cleanNode(v.owned[i]);
              }
              if (v.tOwned)
                v.owned = v.tOwned;
              delete v.tValue;
              delete v.tOwned;
              v.tState = 0;
            }
            setTransPending(false);
          }, false);
        } else if (Transition.running) {
          Transition.running = false;
          Transition.effects.push.apply(Transition.effects, Effects);
          Effects = null;
          setTransPending(true);
          return;
        }
      }
      const e = Effects;
      Effects = null;
      if (e.length)
        runUpdates(() => runEffects(e), false);
      if (res)
        res();
    }
    function runQueue(queue) {
      for (let i = 0; i < queue.length; i++)
        runTop(queue[i]);
    }
    function scheduleQueue(queue) {
      for (let i = 0; i < queue.length; i++) {
        const item = queue[i];
        const tasks = Transition.queue;
        if (!tasks.has(item)) {
          tasks.add(item);
          Scheduler(() => {
            tasks.delete(item);
            runUpdates(() => {
              Transition.running = true;
              runTop(item);
            }, false);
            Transition && (Transition.running = false);
          });
        }
      }
    }
    function runUserEffects(queue) {
      let i, userLength = 0;
      for (i = 0; i < queue.length; i++) {
        const e = queue[i];
        if (!e.user)
          runTop(e);
        else
          queue[userLength++] = e;
      }
      if (sharedConfig.context) {
        if (sharedConfig.count) {
          sharedConfig.effects || (sharedConfig.effects = []);
          sharedConfig.effects.push(...queue.slice(0, userLength));
          return;
        } else if (sharedConfig.effects) {
          queue = [...sharedConfig.effects, ...queue];
          userLength += sharedConfig.effects.length;
          delete sharedConfig.effects;
        }
        setHydrateContext();
      }
      for (i = 0; i < userLength; i++)
        runTop(queue[i]);
    }
    function lookUpstream(node, ignore) {
      const runningTransition = Transition && Transition.running;
      if (runningTransition)
        node.tState = 0;
      else
        node.state = 0;
      for (let i = 0; i < node.sources.length; i += 1) {
        const source = node.sources[i];
        if (source.sources) {
          const state = runningTransition ? source.tState : source.state;
          if (state === STALE) {
            if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount))
              runTop(source);
          } else if (state === PENDING)
            lookUpstream(source, ignore);
        }
      }
    }
    function markDownstream(node) {
      const runningTransition = Transition && Transition.running;
      for (let i = 0; i < node.observers.length; i += 1) {
        const o = node.observers[i];
        if (runningTransition ? !o.tState : !o.state) {
          if (runningTransition)
            o.tState = PENDING;
          else
            o.state = PENDING;
          if (o.pure)
            Updates.push(o);
          else
            Effects.push(o);
          o.observers && markDownstream(o);
        }
      }
    }
    function cleanNode(node) {
      let i;
      if (node.sources) {
        while (node.sources.length) {
          const source = node.sources.pop(), index = node.sourceSlots.pop(), obs = source.observers;
          if (obs && obs.length) {
            const n = obs.pop(), s = source.observerSlots.pop();
            if (index < obs.length) {
              n.sourceSlots[s] = index;
              obs[index] = n;
              source.observerSlots[index] = s;
            }
          }
        }
      }
      if (Transition && Transition.running && node.pure) {
        if (node.tOwned) {
          for (i = node.tOwned.length - 1; i >= 0; i--)
            cleanNode(node.tOwned[i]);
          delete node.tOwned;
        }
        reset(node, true);
      } else if (node.owned) {
        for (i = node.owned.length - 1; i >= 0; i--)
          cleanNode(node.owned[i]);
        node.owned = null;
      }
      if (node.cleanups) {
        for (i = node.cleanups.length - 1; i >= 0; i--)
          node.cleanups[i]();
        node.cleanups = null;
      }
      if (Transition && Transition.running)
        node.tState = 0;
      else
        node.state = 0;
      node.context = null;
    }
    function reset(node, top) {
      if (!top) {
        node.tState = 0;
        Transition.disposed.add(node);
      }
      if (node.owned) {
        for (let i = 0; i < node.owned.length; i++)
          reset(node.owned[i]);
      }
    }
    function castError(err) {
      if (err instanceof Error)
        return err;
      return new Error(typeof err === "string" ? err : "Unknown error", {
        cause: err
      });
    }
    function runErrors(err, fns, owner) {
      try {
        for (const f of fns)
          f(err);
      } catch (e) {
        handleError(e, owner && owner.owner || null);
      }
    }
    function handleError(err, owner = Owner) {
      const fns = ERROR && lookup(owner, ERROR);
      const error = castError(err);
      if (!fns)
        throw error;
      if (Effects)
        Effects.push({
          fn() {
            runErrors(error, fns, owner);
          },
          state: STALE
        });
      else
        runErrors(error, fns, owner);
    }
    function lookup(owner, key) {
      return owner ? owner.context && owner.context[key] !== void 0 ? owner.context[key] : lookup(owner.owner, key) : void 0;
    }
    function resolveChildren(children2) {
      if (typeof children2 === "function" && !children2.length)
        return resolveChildren(children2());
      if (Array.isArray(children2)) {
        const results = [];
        for (let i = 0; i < children2.length; i++) {
          const result = resolveChildren(children2[i]);
          Array.isArray(result) ? results.push.apply(results, result) : results.push(result);
        }
        return results;
      }
      return children2;
    }
    function createProvider(id, options) {
      return function provider(props) {
        let res;
        createRenderEffect(() => res = untrack(() => {
          Owner.context = {
            [id]: props.value
          };
          return children(() => props.children);
        }), void 0);
        return res;
      };
    }
    function observable(input) {
      return {
        subscribe(observer) {
          if (!(observer instanceof Object) || observer == null) {
            throw new TypeError("Expected the observer to be an object.");
          }
          const handler = typeof observer === "function" ? observer : observer.next && observer.next.bind(observer);
          if (!handler) {
            return {
              unsubscribe() {
              }
            };
          }
          const dispose2 = createRoot((disposer) => {
            createEffect(() => {
              const v = input();
              untrack(() => handler(v));
            });
            return disposer;
          });
          if (getOwner())
            onCleanup(dispose2);
          return {
            unsubscribe() {
              dispose2();
            }
          };
        },
        [Symbol.observable || "@@observable"]() {
          return this;
        }
      };
    }
    function from(producer) {
      const [s, set] = createSignal(void 0, {
        equals: false
      });
      if ("subscribe" in producer) {
        const unsub = producer.subscribe((v) => set(() => v));
        onCleanup(() => "unsubscribe" in unsub ? unsub.unsubscribe() : unsub());
      } else {
        const clean = producer(set);
        onCleanup(clean);
      }
      return s;
    }
    var FALLBACK = Symbol("fallback");
    function dispose(d) {
      for (let i = 0; i < d.length; i++)
        d[i]();
    }
    function mapArray(list, mapFn, options = {}) {
      let items = [], mapped = [], disposers = [], len = 0, indexes = mapFn.length > 1 ? [] : null;
      onCleanup(() => dispose(disposers));
      return () => {
        let newItems = list() || [], i, j;
        newItems[$TRACK];
        return untrack(() => {
          let newLen = newItems.length, newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;
          if (newLen === 0) {
            if (len !== 0) {
              dispose(disposers);
              disposers = [];
              items = [];
              mapped = [];
              len = 0;
              indexes && (indexes = []);
            }
            if (options.fallback) {
              items = [FALLBACK];
              mapped[0] = createRoot((disposer) => {
                disposers[0] = disposer;
                return options.fallback();
              });
              len = 1;
            }
          } else if (len === 0) {
            mapped = new Array(newLen);
            for (j = 0; j < newLen; j++) {
              items[j] = newItems[j];
              mapped[j] = createRoot(mapper);
            }
            len = newLen;
          } else {
            temp = new Array(newLen);
            tempdisposers = new Array(newLen);
            indexes && (tempIndexes = new Array(newLen));
            for (start = 0, end = Math.min(len, newLen); start < end && items[start] === newItems[start]; start++)
              ;
            for (end = len - 1, newEnd = newLen - 1; end >= start && newEnd >= start && items[end] === newItems[newEnd]; end--, newEnd--) {
              temp[newEnd] = mapped[end];
              tempdisposers[newEnd] = disposers[end];
              indexes && (tempIndexes[newEnd] = indexes[end]);
            }
            newIndices = /* @__PURE__ */ new Map();
            newIndicesNext = new Array(newEnd + 1);
            for (j = newEnd; j >= start; j--) {
              item = newItems[j];
              i = newIndices.get(item);
              newIndicesNext[j] = i === void 0 ? -1 : i;
              newIndices.set(item, j);
            }
            for (i = start; i <= end; i++) {
              item = items[i];
              j = newIndices.get(item);
              if (j !== void 0 && j !== -1) {
                temp[j] = mapped[i];
                tempdisposers[j] = disposers[i];
                indexes && (tempIndexes[j] = indexes[i]);
                j = newIndicesNext[j];
                newIndices.set(item, j);
              } else
                disposers[i]();
            }
            for (j = start; j < newLen; j++) {
              if (j in temp) {
                mapped[j] = temp[j];
                disposers[j] = tempdisposers[j];
                if (indexes) {
                  indexes[j] = tempIndexes[j];
                  indexes[j](j);
                }
              } else
                mapped[j] = createRoot(mapper);
            }
            mapped = mapped.slice(0, len = newLen);
            items = newItems.slice(0);
          }
          return mapped;
        });
        function mapper(disposer) {
          disposers[j] = disposer;
          if (indexes) {
            const [s, set] = createSignal(j);
            indexes[j] = set;
            return mapFn(newItems[j], s);
          }
          return mapFn(newItems[j]);
        }
      };
    }
    function indexArray(list, mapFn, options = {}) {
      let items = [], mapped = [], disposers = [], signals = [], len = 0, i;
      onCleanup(() => dispose(disposers));
      return () => {
        const newItems = list() || [];
        newItems[$TRACK];
        return untrack(() => {
          if (newItems.length === 0) {
            if (len !== 0) {
              dispose(disposers);
              disposers = [];
              items = [];
              mapped = [];
              len = 0;
              signals = [];
            }
            if (options.fallback) {
              items = [FALLBACK];
              mapped[0] = createRoot((disposer) => {
                disposers[0] = disposer;
                return options.fallback();
              });
              len = 1;
            }
            return mapped;
          }
          if (items[0] === FALLBACK) {
            disposers[0]();
            disposers = [];
            items = [];
            mapped = [];
            len = 0;
          }
          for (i = 0; i < newItems.length; i++) {
            if (i < items.length && items[i] !== newItems[i]) {
              signals[i](() => newItems[i]);
            } else if (i >= items.length) {
              mapped[i] = createRoot(mapper);
            }
          }
          for (; i < items.length; i++) {
            disposers[i]();
          }
          len = signals.length = disposers.length = newItems.length;
          items = newItems.slice(0);
          return mapped = mapped.slice(0, len);
        });
        function mapper(disposer) {
          disposers[i] = disposer;
          const [s, set] = createSignal(newItems[i]);
          signals[i] = set;
          return mapFn(s, i);
        }
      };
    }
    var hydrationEnabled = false;
    function enableHydration() {
      hydrationEnabled = true;
    }
    function createComponent(Comp, props) {
      if (hydrationEnabled) {
        if (sharedConfig.context) {
          const c = sharedConfig.context;
          setHydrateContext(nextHydrateContext());
          const r = untrack(() => Comp(props || {}));
          setHydrateContext(c);
          return r;
        }
      }
      return untrack(() => Comp(props || {}));
    }
    function trueFn() {
      return true;
    }
    var propTraps = {
      get(_, property, receiver) {
        if (property === $PROXY)
          return receiver;
        return _.get(property);
      },
      has(_, property) {
        if (property === $PROXY)
          return true;
        return _.has(property);
      },
      set: trueFn,
      deleteProperty: trueFn,
      getOwnPropertyDescriptor(_, property) {
        return {
          configurable: true,
          enumerable: true,
          get() {
            return _.get(property);
          },
          set: trueFn,
          deleteProperty: trueFn
        };
      },
      ownKeys(_) {
        return _.keys();
      }
    };
    function resolveSource(s) {
      return !(s = typeof s === "function" ? s() : s) ? {} : s;
    }
    function resolveSources() {
      for (let i = 0, length = this.length; i < length; ++i) {
        const v = this[i]();
        if (v !== void 0)
          return v;
      }
    }
    function mergeProps(...sources) {
      let proxy = false;
      for (let i = 0; i < sources.length; i++) {
        const s = sources[i];
        proxy = proxy || !!s && $PROXY in s;
        sources[i] = typeof s === "function" ? (proxy = true, createMemo(s)) : s;
      }
      if (proxy) {
        return new Proxy({
          get(property) {
            for (let i = sources.length - 1; i >= 0; i--) {
              const v = resolveSource(sources[i])[property];
              if (v !== void 0)
                return v;
            }
          },
          has(property) {
            for (let i = sources.length - 1; i >= 0; i--) {
              if (property in resolveSource(sources[i]))
                return true;
            }
            return false;
          },
          keys() {
            const keys = [];
            for (let i = 0; i < sources.length; i++)
              keys.push(...Object.keys(resolveSource(sources[i])));
            return [...new Set(keys)];
          }
        }, propTraps);
      }
      const target = {};
      const sourcesMap = {};
      const defined = /* @__PURE__ */ new Set();
      for (let i = sources.length - 1; i >= 0; i--) {
        const source = sources[i];
        if (!source)
          continue;
        const sourceKeys = Object.getOwnPropertyNames(source);
        for (let i2 = 0, length = sourceKeys.length; i2 < length; i2++) {
          const key = sourceKeys[i2];
          if (key === "__proto__" || key === "constructor")
            continue;
          const desc = Object.getOwnPropertyDescriptor(source, key);
          if (!defined.has(key)) {
            if (desc.get) {
              defined.add(key);
              Object.defineProperty(target, key, {
                enumerable: true,
                configurable: true,
                get: resolveSources.bind(sourcesMap[key] = [desc.get.bind(source)])
              });
            } else {
              if (desc.value !== void 0)
                defined.add(key);
              target[key] = desc.value;
            }
          } else {
            const sources2 = sourcesMap[key];
            if (sources2) {
              if (desc.get) {
                sources2.push(desc.get.bind(source));
              } else if (desc.value !== void 0) {
                sources2.push(() => desc.value);
              }
            } else if (target[key] === void 0)
              target[key] = desc.value;
          }
        }
      }
      return target;
    }
    function splitProps(props, ...keys) {
      if ($PROXY in props) {
        const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);
        const res = keys.map((k) => {
          return new Proxy({
            get(property) {
              return k.includes(property) ? props[property] : void 0;
            },
            has(property) {
              return k.includes(property) && property in props;
            },
            keys() {
              return k.filter((property) => property in props);
            }
          }, propTraps);
        });
        res.push(new Proxy({
          get(property) {
            return blocked.has(property) ? void 0 : props[property];
          },
          has(property) {
            return blocked.has(property) ? false : property in props;
          },
          keys() {
            return Object.keys(props).filter((k) => !blocked.has(k));
          }
        }, propTraps));
        return res;
      }
      const otherObject = {};
      const objects = keys.map(() => ({}));
      for (const propName of Object.getOwnPropertyNames(props)) {
        const desc = Object.getOwnPropertyDescriptor(props, propName);
        const isDefaultDesc = !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;
        let blocked = false;
        let objectIndex = 0;
        for (const k of keys) {
          if (k.includes(propName)) {
            blocked = true;
            isDefaultDesc ? objects[objectIndex][propName] = desc.value : Object.defineProperty(objects[objectIndex], propName, desc);
          }
          ++objectIndex;
        }
        if (!blocked) {
          isDefaultDesc ? otherObject[propName] = desc.value : Object.defineProperty(otherObject, propName, desc);
        }
      }
      return [...objects, otherObject];
    }
    function lazy(fn) {
      let comp;
      let p;
      const wrap = (props) => {
        const ctx = sharedConfig.context;
        if (ctx) {
          const [s, set] = createSignal();
          sharedConfig.count || (sharedConfig.count = 0);
          sharedConfig.count++;
          (p || (p = fn())).then((mod) => {
            setHydrateContext(ctx);
            sharedConfig.count--;
            set(() => mod.default);
            setHydrateContext();
          });
          comp = s;
        } else if (!comp) {
          const [s] = createResource(() => (p || (p = fn())).then((mod) => mod.default));
          comp = s;
        }
        let Comp;
        return createMemo(() => (Comp = comp()) && untrack(() => {
          if (false)
            ;
          if (!ctx)
            return Comp(props);
          const c = sharedConfig.context;
          setHydrateContext(ctx);
          const r = Comp(props);
          setHydrateContext(c);
          return r;
        }));
      };
      wrap.preload = () => p || ((p = fn()).then((mod) => comp = () => mod.default), p);
      return wrap;
    }
    var counter = 0;
    function createUniqueId() {
      const ctx = sharedConfig.context;
      return ctx ? `${ctx.id}${ctx.count++}` : `cl-${counter++}`;
    }
    var narrowedError = (name) => `Stale read from <${name}>.`;
    function For(props) {
      const fallback = "fallback" in props && {
        fallback: () => props.fallback
      };
      return createMemo(mapArray(() => props.each, props.children, fallback || void 0));
    }
    function Index(props) {
      const fallback = "fallback" in props && {
        fallback: () => props.fallback
      };
      return createMemo(indexArray(() => props.each, props.children, fallback || void 0));
    }
    function Show(props) {
      const keyed = props.keyed;
      const condition = createMemo(() => props.when, void 0, {
        equals: (a, b) => keyed ? a === b : !a === !b
      });
      return createMemo(() => {
        const c = condition();
        if (c) {
          const child = props.children;
          const fn = typeof child === "function" && child.length > 0;
          return fn ? untrack(() => child(keyed ? c : () => {
            if (!untrack(condition))
              throw narrowedError("Show");
            return props.when;
          })) : child;
        }
        return props.fallback;
      }, void 0, void 0);
    }
    function Switch(props) {
      let keyed = false;
      const equals = (a, b) => a[0] === b[0] && (keyed ? a[1] === b[1] : !a[1] === !b[1]) && a[2] === b[2];
      const conditions = children(() => props.children), evalConditions = createMemo(() => {
        let conds = conditions();
        if (!Array.isArray(conds))
          conds = [conds];
        for (let i = 0; i < conds.length; i++) {
          const c = conds[i].when;
          if (c) {
            keyed = !!conds[i].keyed;
            return [i, c, conds[i]];
          }
        }
        return [-1];
      }, void 0, {
        equals
      });
      return createMemo(() => {
        const [index, when, cond] = evalConditions();
        if (index < 0)
          return props.fallback;
        const c = cond.children;
        const fn = typeof c === "function" && c.length > 0;
        return fn ? untrack(() => c(keyed ? when : () => {
          if (untrack(evalConditions)[0] !== index)
            throw narrowedError("Match");
          return cond.when;
        })) : c;
      }, void 0, void 0);
    }
    function Match(props) {
      return props;
    }
    var Errors;
    function resetErrorBoundaries() {
      Errors && [...Errors].forEach((fn) => fn());
    }
    function ErrorBoundary(props) {
      let err;
      let v;
      if (sharedConfig.context && sharedConfig.load && (v = sharedConfig.load(sharedConfig.context.id + sharedConfig.context.count)))
        err = v[0];
      const [errored, setErrored] = createSignal(err, void 0);
      Errors || (Errors = /* @__PURE__ */ new Set());
      Errors.add(setErrored);
      onCleanup(() => Errors.delete(setErrored));
      return createMemo(() => {
        let e;
        if (e = errored()) {
          const f = props.fallback;
          return typeof f === "function" && f.length ? untrack(() => f(e, () => setErrored())) : f;
        }
        return catchError(() => props.children, setErrored);
      }, void 0, void 0);
    }
    var suspenseListEquals = (a, b) => a.showContent === b.showContent && a.showFallback === b.showFallback;
    var SuspenseListContext = createContext();
    function SuspenseList(props) {
      let [wrapper, setWrapper] = createSignal(() => ({
        inFallback: false
      })), show;
      const listContext = useContext(SuspenseListContext);
      const [registry, setRegistry] = createSignal([]);
      if (listContext) {
        show = listContext.register(createMemo(() => wrapper()().inFallback));
      }
      const resolved = createMemo((prev) => {
        const reveal = props.revealOrder, tail = props.tail, {
          showContent = true,
          showFallback = true
        } = show ? show() : {}, reg = registry(), reverse = reveal === "backwards";
        if (reveal === "together") {
          const all = reg.every((inFallback2) => !inFallback2());
          const res2 = reg.map(() => ({
            showContent: all && showContent,
            showFallback
          }));
          res2.inFallback = !all;
          return res2;
        }
        let stop = false;
        let inFallback = prev.inFallback;
        const res = [];
        for (let i = 0, len = reg.length; i < len; i++) {
          const n = reverse ? len - i - 1 : i, s = reg[n]();
          if (!stop && !s) {
            res[n] = {
              showContent,
              showFallback
            };
          } else {
            const next = !stop;
            if (next)
              inFallback = true;
            res[n] = {
              showContent: next,
              showFallback: !tail || next && tail === "collapsed" ? showFallback : false
            };
            stop = true;
          }
        }
        if (!stop)
          inFallback = false;
        res.inFallback = inFallback;
        return res;
      }, {
        inFallback: false
      });
      setWrapper(() => resolved);
      return createComponent(SuspenseListContext.Provider, {
        value: {
          register: (inFallback) => {
            let index;
            setRegistry((registry2) => {
              index = registry2.length;
              return [...registry2, inFallback];
            });
            return createMemo(() => resolved()[index], void 0, {
              equals: suspenseListEquals
            });
          }
        },
        get children() {
          return props.children;
        }
      });
    }
    function Suspense(props) {
      let counter2 = 0, show, ctx, p, flicker, error;
      const [inFallback, setFallback] = createSignal(false), SuspenseContext2 = getSuspenseContext(), store = {
        increment: () => {
          if (++counter2 === 1)
            setFallback(true);
        },
        decrement: () => {
          if (--counter2 === 0)
            setFallback(false);
        },
        inFallback,
        effects: [],
        resolved: false
      }, owner = getOwner();
      if (sharedConfig.context && sharedConfig.load) {
        const key = sharedConfig.context.id + sharedConfig.context.count;
        let ref = sharedConfig.load(key);
        if (ref && (p = ref[0]) && p !== "$$f") {
          if (typeof p !== "object" || !("then" in p))
            p = Promise.resolve(p);
          const [s, set] = createSignal(void 0, {
            equals: false
          });
          flicker = s;
          p.then((err) => {
            if (err || sharedConfig.done) {
              err && (error = err);
              return set();
            }
            sharedConfig.gather(key);
            setHydrateContext(ctx);
            set();
            setHydrateContext();
          });
        }
      }
      const listContext = useContext(SuspenseListContext);
      if (listContext)
        show = listContext.register(store.inFallback);
      let dispose2;
      onCleanup(() => dispose2 && dispose2());
      return createComponent(SuspenseContext2.Provider, {
        value: store,
        get children() {
          return createMemo(() => {
            if (error)
              throw error;
            ctx = sharedConfig.context;
            if (flicker) {
              flicker();
              return flicker = void 0;
            }
            if (ctx && p === "$$f")
              setHydrateContext();
            const rendered = createMemo(() => props.children);
            return createMemo((prev) => {
              const inFallback2 = store.inFallback(), {
                showContent = true,
                showFallback = true
              } = show ? show() : {};
              if ((!inFallback2 || p && p !== "$$f") && showContent) {
                store.resolved = true;
                dispose2 && dispose2();
                dispose2 = ctx = p = void 0;
                resumeEffects(store.effects);
                return rendered();
              }
              if (!showFallback)
                return;
              if (dispose2)
                return prev;
              return createRoot((disposer) => {
                dispose2 = disposer;
                if (ctx) {
                  setHydrateContext({
                    id: ctx.id + "f",
                    count: 0
                  });
                  ctx = void 0;
                }
                return props.fallback;
              }, owner);
            });
          });
        }
      });
    }
    var DEV = void 0;
    exports.$DEVCOMP = $DEVCOMP;
    exports.$PROXY = $PROXY;
    exports.$TRACK = $TRACK;
    exports.DEV = DEV;
    exports.ErrorBoundary = ErrorBoundary;
    exports.For = For;
    exports.Index = Index;
    exports.Match = Match;
    exports.Show = Show;
    exports.Suspense = Suspense;
    exports.SuspenseList = SuspenseList;
    exports.Switch = Switch;
    exports.batch = batch;
    exports.cancelCallback = cancelCallback;
    exports.catchError = catchError;
    exports.children = children;
    exports.createComponent = createComponent;
    exports.createComputed = createComputed;
    exports.createContext = createContext;
    exports.createDeferred = createDeferred;
    exports.createEffect = createEffect;
    exports.createMemo = createMemo;
    exports.createReaction = createReaction;
    exports.createRenderEffect = createRenderEffect;
    exports.createResource = createResource;
    exports.createRoot = createRoot;
    exports.createSelector = createSelector;
    exports.createSignal = createSignal;
    exports.createUniqueId = createUniqueId;
    exports.enableExternalSource = enableExternalSource;
    exports.enableHydration = enableHydration;
    exports.enableScheduling = enableScheduling;
    exports.equalFn = equalFn;
    exports.from = from;
    exports.getListener = getListener;
    exports.getOwner = getOwner;
    exports.indexArray = indexArray;
    exports.lazy = lazy;
    exports.mapArray = mapArray;
    exports.mergeProps = mergeProps;
    exports.observable = observable;
    exports.on = on;
    exports.onCleanup = onCleanup;
    exports.onError = onError;
    exports.onMount = onMount;
    exports.requestCallback = requestCallback;
    exports.resetErrorBoundaries = resetErrorBoundaries;
    exports.runWithOwner = runWithOwner;
    exports.sharedConfig = sharedConfig;
    exports.splitProps = splitProps;
    exports.startTransition = startTransition;
    exports.untrack = untrack;
    exports.useContext = useContext;
    exports.useTransition = useTransition;
  }
});

// node_modules/solid-js/web/dist/web.cjs
var require_web = __commonJS({
  "node_modules/solid-js/web/dist/web.cjs"(exports) {
    "use strict";
    var solidJs = require_solid();
    var booleans = ["allowfullscreen", "async", "autofocus", "autoplay", "checked", "controls", "default", "disabled", "formnovalidate", "hidden", "indeterminate", "ismap", "loop", "multiple", "muted", "nomodule", "novalidate", "open", "playsinline", "readonly", "required", "reversed", "seamless", "selected"];
    var Properties = /* @__PURE__ */ new Set(["className", "value", "readOnly", "formNoValidate", "isMap", "noModule", "playsInline", ...booleans]);
    var ChildProperties = /* @__PURE__ */ new Set(["innerHTML", "textContent", "innerText", "children"]);
    var Aliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {
      className: "class",
      htmlFor: "for"
    });
    var PropAliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {
      class: "className",
      formnovalidate: {
        $: "formNoValidate",
        BUTTON: 1,
        INPUT: 1
      },
      ismap: {
        $: "isMap",
        IMG: 1
      },
      nomodule: {
        $: "noModule",
        SCRIPT: 1
      },
      playsinline: {
        $: "playsInline",
        VIDEO: 1
      },
      readonly: {
        $: "readOnly",
        INPUT: 1,
        TEXTAREA: 1
      }
    });
    function getPropAlias(prop, tagName) {
      const a = PropAliases[prop];
      return typeof a === "object" ? a[tagName] ? a["$"] : void 0 : a;
    }
    var DelegatedEvents = /* @__PURE__ */ new Set(["beforeinput", "click", "dblclick", "contextmenu", "focusin", "focusout", "input", "keydown", "keyup", "mousedown", "mousemove", "mouseout", "mouseover", "mouseup", "pointerdown", "pointermove", "pointerout", "pointerover", "pointerup", "touchend", "touchmove", "touchstart"]);
    var SVGElements = /* @__PURE__ */ new Set([
      "altGlyph",
      "altGlyphDef",
      "altGlyphItem",
      "animate",
      "animateColor",
      "animateMotion",
      "animateTransform",
      "circle",
      "clipPath",
      "color-profile",
      "cursor",
      "defs",
      "desc",
      "ellipse",
      "feBlend",
      "feColorMatrix",
      "feComponentTransfer",
      "feComposite",
      "feConvolveMatrix",
      "feDiffuseLighting",
      "feDisplacementMap",
      "feDistantLight",
      "feFlood",
      "feFuncA",
      "feFuncB",
      "feFuncG",
      "feFuncR",
      "feGaussianBlur",
      "feImage",
      "feMerge",
      "feMergeNode",
      "feMorphology",
      "feOffset",
      "fePointLight",
      "feSpecularLighting",
      "feSpotLight",
      "feTile",
      "feTurbulence",
      "filter",
      "font",
      "font-face",
      "font-face-format",
      "font-face-name",
      "font-face-src",
      "font-face-uri",
      "foreignObject",
      "g",
      "glyph",
      "glyphRef",
      "hkern",
      "image",
      "line",
      "linearGradient",
      "marker",
      "mask",
      "metadata",
      "missing-glyph",
      "mpath",
      "path",
      "pattern",
      "polygon",
      "polyline",
      "radialGradient",
      "rect",
      "set",
      "stop",
      "svg",
      "switch",
      "symbol",
      "text",
      "textPath",
      "tref",
      "tspan",
      "use",
      "view",
      "vkern"
    ]);
    var SVGNamespace = {
      xlink: "http://www.w3.org/1999/xlink",
      xml: "http://www.w3.org/XML/1998/namespace"
    };
    var DOMElements = /* @__PURE__ */ new Set(["html", "base", "head", "link", "meta", "style", "title", "body", "address", "article", "aside", "footer", "header", "main", "nav", "section", "body", "blockquote", "dd", "div", "dl", "dt", "figcaption", "figure", "hr", "li", "ol", "p", "pre", "ul", "a", "abbr", "b", "bdi", "bdo", "br", "cite", "code", "data", "dfn", "em", "i", "kbd", "mark", "q", "rp", "rt", "ruby", "s", "samp", "small", "span", "strong", "sub", "sup", "time", "u", "var", "wbr", "area", "audio", "img", "map", "track", "video", "embed", "iframe", "object", "param", "picture", "portal", "source", "svg", "math", "canvas", "noscript", "script", "del", "ins", "caption", "col", "colgroup", "table", "tbody", "td", "tfoot", "th", "thead", "tr", "button", "datalist", "fieldset", "form", "input", "label", "legend", "meter", "optgroup", "option", "output", "progress", "select", "textarea", "details", "dialog", "menu", "summary", "details", "slot", "template", "acronym", "applet", "basefont", "bgsound", "big", "blink", "center", "content", "dir", "font", "frame", "frameset", "hgroup", "image", "keygen", "marquee", "menuitem", "nobr", "noembed", "noframes", "plaintext", "rb", "rtc", "shadow", "spacer", "strike", "tt", "xmp", "a", "abbr", "acronym", "address", "applet", "area", "article", "aside", "audio", "b", "base", "basefont", "bdi", "bdo", "bgsound", "big", "blink", "blockquote", "body", "br", "button", "canvas", "caption", "center", "cite", "code", "col", "colgroup", "content", "data", "datalist", "dd", "del", "details", "dfn", "dialog", "dir", "div", "dl", "dt", "em", "embed", "fieldset", "figcaption", "figure", "font", "footer", "form", "frame", "frameset", "head", "header", "hgroup", "hr", "html", "i", "iframe", "image", "img", "input", "ins", "kbd", "keygen", "label", "legend", "li", "link", "main", "map", "mark", "marquee", "menu", "menuitem", "meta", "meter", "nav", "nobr", "noembed", "noframes", "noscript", "object", "ol", "optgroup", "option", "output", "p", "param", "picture", "plaintext", "portal", "pre", "progress", "q", "rb", "rp", "rt", "rtc", "ruby", "s", "samp", "script", "section", "select", "shadow", "slot", "small", "source", "spacer", "span", "strike", "strong", "style", "sub", "summary", "sup", "table", "tbody", "td", "template", "textarea", "tfoot", "th", "thead", "time", "title", "tr", "track", "tt", "u", "ul", "var", "video", "wbr", "xmp", "input", "h1", "h2", "h3", "h4", "h5", "h6"]);
    function reconcileArrays(parentNode, a, b) {
      let bLength = b.length, aEnd = a.length, bEnd = bLength, aStart = 0, bStart = 0, after = a[aEnd - 1].nextSibling, map = null;
      while (aStart < aEnd || bStart < bEnd) {
        if (a[aStart] === b[bStart]) {
          aStart++;
          bStart++;
          continue;
        }
        while (a[aEnd - 1] === b[bEnd - 1]) {
          aEnd--;
          bEnd--;
        }
        if (aEnd === aStart) {
          const node = bEnd < bLength ? bStart ? b[bStart - 1].nextSibling : b[bEnd - bStart] : after;
          while (bStart < bEnd)
            parentNode.insertBefore(b[bStart++], node);
        } else if (bEnd === bStart) {
          while (aStart < aEnd) {
            if (!map || !map.has(a[aStart]))
              a[aStart].remove();
            aStart++;
          }
        } else if (a[aStart] === b[bEnd - 1] && b[bStart] === a[aEnd - 1]) {
          const node = a[--aEnd].nextSibling;
          parentNode.insertBefore(b[bStart++], a[aStart++].nextSibling);
          parentNode.insertBefore(b[--bEnd], node);
          a[aEnd] = b[bEnd];
        } else {
          if (!map) {
            map = /* @__PURE__ */ new Map();
            let i = bStart;
            while (i < bEnd)
              map.set(b[i], i++);
          }
          const index = map.get(a[aStart]);
          if (index != null) {
            if (bStart < index && index < bEnd) {
              let i = aStart, sequence = 1, t;
              while (++i < aEnd && i < bEnd) {
                if ((t = map.get(a[i])) == null || t !== index + sequence)
                  break;
                sequence++;
              }
              if (sequence > index - bStart) {
                const node = a[aStart];
                while (bStart < index)
                  parentNode.insertBefore(b[bStart++], node);
              } else
                parentNode.replaceChild(b[bStart++], a[aStart++]);
            } else
              aStart++;
          } else
            a[aStart++].remove();
        }
      }
    }
    var $$EVENTS = "_$DX_DELEGATE";
    function render(code, element, init, options = {}) {
      let disposer;
      solidJs.createRoot((dispose) => {
        disposer = dispose;
        element === document ? code() : insert(element, code(), element.firstChild ? null : void 0, init);
      }, options.owner);
      return () => {
        disposer();
        element.textContent = "";
      };
    }
    function template(html, isCE, isSVG) {
      let node;
      const create = () => {
        const t = document.createElement("template");
        t.innerHTML = html;
        return isSVG ? t.content.firstChild.firstChild : t.content.firstChild;
      };
      const fn = isCE ? () => solidJs.untrack(() => document.importNode(node || (node = create()), true)) : () => (node || (node = create())).cloneNode(true);
      fn.cloneNode = fn;
      return fn;
    }
    function delegateEvents(eventNames, document2 = window.document) {
      const e = document2[$$EVENTS] || (document2[$$EVENTS] = /* @__PURE__ */ new Set());
      for (let i = 0, l = eventNames.length; i < l; i++) {
        const name = eventNames[i];
        if (!e.has(name)) {
          e.add(name);
          document2.addEventListener(name, eventHandler);
        }
      }
    }
    function clearDelegatedEvents(document2 = window.document) {
      if (document2[$$EVENTS]) {
        for (let name of document2[$$EVENTS].keys())
          document2.removeEventListener(name, eventHandler);
        delete document2[$$EVENTS];
      }
    }
    function setAttribute(node, name, value) {
      if (value == null)
        node.removeAttribute(name);
      else
        node.setAttribute(name, value);
    }
    function setAttributeNS(node, namespace, name, value) {
      if (value == null)
        node.removeAttributeNS(namespace, name);
      else
        node.setAttributeNS(namespace, name, value);
    }
    function className(node, value) {
      if (value == null)
        node.removeAttribute("class");
      else
        node.className = value;
    }
    function addEventListener(node, name, handler, delegate) {
      if (delegate) {
        if (Array.isArray(handler)) {
          node[`$$${name}`] = handler[0];
          node[`$$${name}Data`] = handler[1];
        } else
          node[`$$${name}`] = handler;
      } else if (Array.isArray(handler)) {
        const handlerFn = handler[0];
        node.addEventListener(name, handler[0] = (e) => handlerFn.call(node, handler[1], e));
      } else
        node.addEventListener(name, handler);
    }
    function classList(node, value, prev = {}) {
      const classKeys = Object.keys(value || {}), prevKeys = Object.keys(prev);
      let i, len;
      for (i = 0, len = prevKeys.length; i < len; i++) {
        const key = prevKeys[i];
        if (!key || key === "undefined" || value[key])
          continue;
        toggleClassKey(node, key, false);
        delete prev[key];
      }
      for (i = 0, len = classKeys.length; i < len; i++) {
        const key = classKeys[i], classValue = !!value[key];
        if (!key || key === "undefined" || prev[key] === classValue || !classValue)
          continue;
        toggleClassKey(node, key, true);
        prev[key] = classValue;
      }
      return prev;
    }
    function style(node, value, prev) {
      if (!value)
        return prev ? setAttribute(node, "style") : value;
      const nodeStyle = node.style;
      if (typeof value === "string")
        return nodeStyle.cssText = value;
      typeof prev === "string" && (nodeStyle.cssText = prev = void 0);
      prev || (prev = {});
      value || (value = {});
      let v, s;
      for (s in prev) {
        value[s] == null && nodeStyle.removeProperty(s);
        delete prev[s];
      }
      for (s in value) {
        v = value[s];
        if (v !== prev[s]) {
          nodeStyle.setProperty(s, v);
          prev[s] = v;
        }
      }
      return prev;
    }
    function spread(node, props = {}, isSVG, skipChildren) {
      const prevProps = {};
      if (!skipChildren) {
        solidJs.createRenderEffect(() => prevProps.children = insertExpression(node, props.children, prevProps.children));
      }
      solidJs.createRenderEffect(() => props.ref && props.ref(node));
      solidJs.createRenderEffect(() => assign(node, props, isSVG, true, prevProps, true));
      return prevProps;
    }
    function dynamicProperty(props, key) {
      const src = props[key];
      Object.defineProperty(props, key, {
        get() {
          return src();
        },
        enumerable: true
      });
      return props;
    }
    function innerHTML(parent, content) {
      !solidJs.sharedConfig.context && (parent.innerHTML = content);
    }
    function use(fn, element, arg) {
      return solidJs.untrack(() => fn(element, arg));
    }
    function insert(parent, accessor, marker, initial) {
      if (marker !== void 0 && !initial)
        initial = [];
      if (typeof accessor !== "function")
        return insertExpression(parent, accessor, initial, marker);
      solidJs.createRenderEffect((current) => insertExpression(parent, accessor(), current, marker), initial);
    }
    function assign(node, props, isSVG, skipChildren, prevProps = {}, skipRef = false) {
      props || (props = {});
      for (const prop in prevProps) {
        if (!(prop in props)) {
          if (prop === "children")
            continue;
          prevProps[prop] = assignProp(node, prop, null, prevProps[prop], isSVG, skipRef);
        }
      }
      for (const prop in props) {
        if (prop === "children") {
          if (!skipChildren)
            insertExpression(node, props.children);
          continue;
        }
        const value = props[prop];
        prevProps[prop] = assignProp(node, prop, value, prevProps[prop], isSVG, skipRef);
      }
    }
    function hydrate$1(code, element, options = {}) {
      solidJs.sharedConfig.completed = globalThis._$HY.completed;
      solidJs.sharedConfig.events = globalThis._$HY.events;
      solidJs.sharedConfig.load = globalThis._$HY.load;
      solidJs.sharedConfig.gather = (root) => gatherHydratable(element, root);
      solidJs.sharedConfig.registry = /* @__PURE__ */ new Map();
      solidJs.sharedConfig.context = {
        id: options.renderId || "",
        count: 0
      };
      gatherHydratable(element, options.renderId);
      const dispose = render(code, element, [...element.childNodes], options);
      solidJs.sharedConfig.context = null;
      return dispose;
    }
    function getNextElement(template2) {
      let node, key;
      if (!solidJs.sharedConfig.context || !(node = solidJs.sharedConfig.registry.get(key = getHydrationKey()))) {
        if (solidJs.sharedConfig.context)
          console.warn("Unable to find DOM nodes for hydration key:", key);
        if (!template2)
          throw new Error("Unrecoverable Hydration Mismatch. No template for key: " + key);
        return template2();
      }
      if (solidJs.sharedConfig.completed)
        solidJs.sharedConfig.completed.add(node);
      solidJs.sharedConfig.registry.delete(key);
      return node;
    }
    function getNextMatch(el, nodeName) {
      while (el && el.localName !== nodeName)
        el = el.nextSibling;
      return el;
    }
    function getNextMarker(start) {
      let end = start, count = 0, current = [];
      if (solidJs.sharedConfig.context) {
        while (end) {
          if (end.nodeType === 8) {
            const v = end.nodeValue;
            if (v === "#")
              count++;
            else if (v === "/") {
              if (count === 0)
                return [end, current];
              count--;
            }
          }
          current.push(end);
          end = end.nextSibling;
        }
      }
      return [end, current];
    }
    function runHydrationEvents() {
      if (solidJs.sharedConfig.events && !solidJs.sharedConfig.events.queued) {
        queueMicrotask(() => {
          const {
            completed,
            events
          } = solidJs.sharedConfig;
          events.queued = false;
          while (events.length) {
            const [el, e] = events[0];
            if (!completed.has(el))
              return;
            eventHandler(e);
            events.shift();
          }
        });
        solidJs.sharedConfig.events.queued = true;
      }
    }
    function toPropertyName(name) {
      return name.toLowerCase().replace(/-([a-z])/g, (_, w) => w.toUpperCase());
    }
    function toggleClassKey(node, key, value) {
      const classNames = key.trim().split(/\s+/);
      for (let i = 0, nameLen = classNames.length; i < nameLen; i++)
        node.classList.toggle(classNames[i], value);
    }
    function assignProp(node, prop, value, prev, isSVG, skipRef) {
      let isCE, isProp, isChildProp, propAlias, forceProp;
      if (prop === "style")
        return style(node, value, prev);
      if (prop === "classList")
        return classList(node, value, prev);
      if (value === prev)
        return prev;
      if (prop === "ref") {
        if (!skipRef)
          value(node);
      } else if (prop.slice(0, 3) === "on:") {
        const e = prop.slice(3);
        prev && node.removeEventListener(e, prev);
        value && node.addEventListener(e, value);
      } else if (prop.slice(0, 10) === "oncapture:") {
        const e = prop.slice(10);
        prev && node.removeEventListener(e, prev, true);
        value && node.addEventListener(e, value, true);
      } else if (prop.slice(0, 2) === "on") {
        const name = prop.slice(2).toLowerCase();
        const delegate = DelegatedEvents.has(name);
        if (!delegate && prev) {
          const h = Array.isArray(prev) ? prev[0] : prev;
          node.removeEventListener(name, h);
        }
        if (delegate || value) {
          addEventListener(node, name, value, delegate);
          delegate && delegateEvents([name]);
        }
      } else if (prop.slice(0, 5) === "attr:") {
        setAttribute(node, prop.slice(5), value);
      } else if ((forceProp = prop.slice(0, 5) === "prop:") || (isChildProp = ChildProperties.has(prop)) || !isSVG && ((propAlias = getPropAlias(prop, node.tagName)) || (isProp = Properties.has(prop))) || (isCE = node.nodeName.includes("-"))) {
        if (forceProp) {
          prop = prop.slice(5);
          isProp = true;
        }
        if (prop === "class" || prop === "className")
          className(node, value);
        else if (isCE && !isProp && !isChildProp)
          node[toPropertyName(prop)] = value;
        else
          node[propAlias || prop] = value;
      } else {
        const ns = isSVG && prop.indexOf(":") > -1 && SVGNamespace[prop.split(":")[0]];
        if (ns)
          setAttributeNS(node, ns, prop, value);
        else
          setAttribute(node, Aliases[prop] || prop, value);
      }
      return value;
    }
    function eventHandler(e) {
      const key = `$$${e.type}`;
      let node = e.composedPath && e.composedPath()[0] || e.target;
      if (e.target !== node) {
        Object.defineProperty(e, "target", {
          configurable: true,
          value: node
        });
      }
      Object.defineProperty(e, "currentTarget", {
        configurable: true,
        get() {
          return node || document;
        }
      });
      if (solidJs.sharedConfig.registry && !solidJs.sharedConfig.done)
        solidJs.sharedConfig.done = _$HY.done = true;
      while (node) {
        const handler = node[key];
        if (handler && !node.disabled) {
          const data = node[`${key}Data`];
          data !== void 0 ? handler.call(node, data, e) : handler.call(node, e);
          if (e.cancelBubble)
            return;
        }
        node = node._$host || node.parentNode || node.host;
      }
    }
    function insertExpression(parent, value, current, marker, unwrapArray) {
      if (solidJs.sharedConfig.context) {
        !current && (current = [...parent.childNodes]);
        let cleaned = [];
        for (let i = 0; i < current.length; i++) {
          const node = current[i];
          if (node.nodeType === 8 && node.data.slice(0, 2) === "!$")
            node.remove();
          else
            cleaned.push(node);
        }
        current = cleaned;
      }
      while (typeof current === "function")
        current = current();
      if (value === current)
        return current;
      const t = typeof value, multi = marker !== void 0;
      parent = multi && current[0] && current[0].parentNode || parent;
      if (t === "string" || t === "number") {
        if (solidJs.sharedConfig.context)
          return current;
        if (t === "number")
          value = value.toString();
        if (multi) {
          let node = current[0];
          if (node && node.nodeType === 3) {
            node.data = value;
          } else
            node = document.createTextNode(value);
          current = cleanChildren(parent, current, marker, node);
        } else {
          if (current !== "" && typeof current === "string") {
            current = parent.firstChild.data = value;
          } else
            current = parent.textContent = value;
        }
      } else if (value == null || t === "boolean") {
        if (solidJs.sharedConfig.context)
          return current;
        current = cleanChildren(parent, current, marker);
      } else if (t === "function") {
        solidJs.createRenderEffect(() => {
          let v = value();
          while (typeof v === "function")
            v = v();
          current = insertExpression(parent, v, current, marker);
        });
        return () => current;
      } else if (Array.isArray(value)) {
        const array = [];
        const currentArray = current && Array.isArray(current);
        if (normalizeIncomingArray(array, value, current, unwrapArray)) {
          solidJs.createRenderEffect(() => current = insertExpression(parent, array, current, marker, true));
          return () => current;
        }
        if (solidJs.sharedConfig.context) {
          if (!array.length)
            return current;
          for (let i = 0; i < array.length; i++) {
            if (array[i].parentNode)
              return current = array;
          }
        }
        if (array.length === 0) {
          current = cleanChildren(parent, current, marker);
          if (multi)
            return current;
        } else if (currentArray) {
          if (current.length === 0) {
            appendNodes(parent, array, marker);
          } else
            reconcileArrays(parent, current, array);
        } else {
          current && cleanChildren(parent);
          appendNodes(parent, array);
        }
        current = array;
      } else if (value.nodeType) {
        if (solidJs.sharedConfig.context && value.parentNode)
          return current = multi ? [value] : value;
        if (Array.isArray(current)) {
          if (multi)
            return current = cleanChildren(parent, current, marker, value);
          cleanChildren(parent, current, null, value);
        } else if (current == null || current === "" || !parent.firstChild) {
          parent.appendChild(value);
        } else
          parent.replaceChild(value, parent.firstChild);
        current = value;
      } else
        console.warn(`Unrecognized value. Skipped inserting`, value);
      return current;
    }
    function normalizeIncomingArray(normalized, array, current, unwrap) {
      let dynamic = false;
      for (let i = 0, len = array.length; i < len; i++) {
        let item = array[i], prev = current && current[i], t;
        if (item == null || item === true || item === false)
          ;
        else if ((t = typeof item) === "object" && item.nodeType) {
          normalized.push(item);
        } else if (Array.isArray(item)) {
          dynamic = normalizeIncomingArray(normalized, item, prev) || dynamic;
        } else if (t === "function") {
          if (unwrap) {
            while (typeof item === "function")
              item = item();
            dynamic = normalizeIncomingArray(normalized, Array.isArray(item) ? item : [item], Array.isArray(prev) ? prev : [prev]) || dynamic;
          } else {
            normalized.push(item);
            dynamic = true;
          }
        } else {
          const value = String(item);
          if (prev && prev.nodeType === 3 && prev.data === value)
            normalized.push(prev);
          else
            normalized.push(document.createTextNode(value));
        }
      }
      return dynamic;
    }
    function appendNodes(parent, array, marker = null) {
      for (let i = 0, len = array.length; i < len; i++)
        parent.insertBefore(array[i], marker);
    }
    function cleanChildren(parent, current, marker, replacement) {
      if (marker === void 0)
        return parent.textContent = "";
      const node = replacement || document.createTextNode("");
      if (current.length) {
        let inserted = false;
        for (let i = current.length - 1; i >= 0; i--) {
          const el = current[i];
          if (node !== el) {
            const isParent = el.parentNode === parent;
            if (!inserted && !i)
              isParent ? parent.replaceChild(node, el) : parent.insertBefore(node, marker);
            else
              isParent && el.remove();
          } else
            inserted = true;
        }
      } else
        parent.insertBefore(node, marker);
      return [node];
    }
    function gatherHydratable(element, root) {
      const templates = element.querySelectorAll(`*[data-hk]`);
      for (let i = 0; i < templates.length; i++) {
        const node = templates[i];
        const key = node.getAttribute("data-hk");
        if ((!root || key.startsWith(root)) && !solidJs.sharedConfig.registry.has(key))
          solidJs.sharedConfig.registry.set(key, node);
      }
    }
    function getHydrationKey() {
      const hydrate2 = solidJs.sharedConfig.context;
      return `${hydrate2.id}${hydrate2.count++}`;
    }
    function NoHydration(props) {
      return solidJs.sharedConfig.context ? void 0 : props.children;
    }
    function Hydration(props) {
      return props.children;
    }
    function voidFn() {
    }
    function throwInBrowser(func) {
      const err = new Error(`${func.name} is not supported in the browser, returning undefined`);
      console.error(err);
    }
    function renderToString(fn, options) {
      throwInBrowser(renderToString);
    }
    function renderToStringAsync(fn, options) {
      throwInBrowser(renderToStringAsync);
    }
    function renderToStream(fn, options) {
      throwInBrowser(renderToStream);
    }
    function ssr(template2, ...nodes) {
    }
    function ssrElement(name, props, children, needsId) {
    }
    function ssrClassList(value) {
    }
    function ssrStyle(value) {
    }
    function ssrAttribute(key, value) {
    }
    function ssrHydrationKey() {
    }
    function resolveSSRNode(node) {
    }
    function escape(html) {
    }
    function ssrSpread(props, isSVG, skipChildren) {
    }
    var isServer = false;
    var isDev = false;
    var SVG_NAMESPACE = "http://www.w3.org/2000/svg";
    function createElement(tagName, isSVG = false) {
      return isSVG ? document.createElementNS(SVG_NAMESPACE, tagName) : document.createElement(tagName);
    }
    var hydrate = (...args) => {
      solidJs.enableHydration();
      return hydrate$1(...args);
    };
    function Portal(props) {
      const {
        useShadow
      } = props, marker = document.createTextNode(""), mount = () => props.mount || document.body, owner = solidJs.getOwner();
      let content;
      let hydrating = !!solidJs.sharedConfig.context;
      solidJs.createEffect(() => {
        if (hydrating)
          solidJs.getOwner().user = hydrating = false;
        content || (content = solidJs.runWithOwner(owner, () => solidJs.createMemo(() => props.children)));
        const el = mount();
        if (el instanceof HTMLHeadElement) {
          const [clean, setClean] = solidJs.createSignal(false);
          const cleanup = () => setClean(true);
          solidJs.createRoot((dispose) => insert(el, () => !clean() ? content() : dispose(), null));
          solidJs.onCleanup(cleanup);
        } else {
          const container = createElement(props.isSVG ? "g" : "div", props.isSVG), renderRoot = useShadow && container.attachShadow ? container.attachShadow({
            mode: "open"
          }) : container;
          Object.defineProperty(container, "_$host", {
            get() {
              return marker.parentNode;
            },
            configurable: true
          });
          insert(renderRoot, content);
          el.appendChild(container);
          props.ref && props.ref(container);
          solidJs.onCleanup(() => el.removeChild(container));
        }
      }, void 0, {
        render: !hydrating
      });
      return marker;
    }
    function Dynamic(props) {
      const [p, others] = solidJs.splitProps(props, ["component"]);
      const cached = solidJs.createMemo(() => p.component);
      return solidJs.createMemo(() => {
        const component = cached();
        switch (typeof component) {
          case "function":
            Object.assign(component, {
              [solidJs.$DEVCOMP]: true
            });
            return solidJs.untrack(() => component(others));
          case "string":
            const isSvg = SVGElements.has(component);
            const el = solidJs.sharedConfig.context ? getNextElement() : createElement(component, isSvg);
            spread(el, others, isSvg);
            return el;
        }
      });
    }
    Object.defineProperty(exports, "ErrorBoundary", {
      enumerable: true,
      get: function() {
        return solidJs.ErrorBoundary;
      }
    });
    Object.defineProperty(exports, "For", {
      enumerable: true,
      get: function() {
        return solidJs.For;
      }
    });
    Object.defineProperty(exports, "Index", {
      enumerable: true,
      get: function() {
        return solidJs.Index;
      }
    });
    Object.defineProperty(exports, "Match", {
      enumerable: true,
      get: function() {
        return solidJs.Match;
      }
    });
    Object.defineProperty(exports, "Show", {
      enumerable: true,
      get: function() {
        return solidJs.Show;
      }
    });
    Object.defineProperty(exports, "Suspense", {
      enumerable: true,
      get: function() {
        return solidJs.Suspense;
      }
    });
    Object.defineProperty(exports, "SuspenseList", {
      enumerable: true,
      get: function() {
        return solidJs.SuspenseList;
      }
    });
    Object.defineProperty(exports, "Switch", {
      enumerable: true,
      get: function() {
        return solidJs.Switch;
      }
    });
    Object.defineProperty(exports, "createComponent", {
      enumerable: true,
      get: function() {
        return solidJs.createComponent;
      }
    });
    Object.defineProperty(exports, "effect", {
      enumerable: true,
      get: function() {
        return solidJs.createRenderEffect;
      }
    });
    Object.defineProperty(exports, "getOwner", {
      enumerable: true,
      get: function() {
        return solidJs.getOwner;
      }
    });
    Object.defineProperty(exports, "memo", {
      enumerable: true,
      get: function() {
        return solidJs.createMemo;
      }
    });
    Object.defineProperty(exports, "mergeProps", {
      enumerable: true,
      get: function() {
        return solidJs.mergeProps;
      }
    });
    Object.defineProperty(exports, "untrack", {
      enumerable: true,
      get: function() {
        return solidJs.untrack;
      }
    });
    exports.Aliases = Aliases;
    exports.Assets = voidFn;
    exports.ChildProperties = ChildProperties;
    exports.DOMElements = DOMElements;
    exports.DelegatedEvents = DelegatedEvents;
    exports.Dynamic = Dynamic;
    exports.Hydration = Hydration;
    exports.HydrationScript = voidFn;
    exports.NoHydration = NoHydration;
    exports.Portal = Portal;
    exports.Properties = Properties;
    exports.SVGElements = SVGElements;
    exports.SVGNamespace = SVGNamespace;
    exports.addEventListener = addEventListener;
    exports.assign = assign;
    exports.classList = classList;
    exports.className = className;
    exports.clearDelegatedEvents = clearDelegatedEvents;
    exports.delegateEvents = delegateEvents;
    exports.dynamicProperty = dynamicProperty;
    exports.escape = escape;
    exports.generateHydrationScript = voidFn;
    exports.getAssets = voidFn;
    exports.getHydrationKey = getHydrationKey;
    exports.getNextElement = getNextElement;
    exports.getNextMarker = getNextMarker;
    exports.getNextMatch = getNextMatch;
    exports.getPropAlias = getPropAlias;
    exports.hydrate = hydrate;
    exports.innerHTML = innerHTML;
    exports.insert = insert;
    exports.isDev = isDev;
    exports.isServer = isServer;
    exports.render = render;
    exports.renderToStream = renderToStream;
    exports.renderToString = renderToString;
    exports.renderToStringAsync = renderToStringAsync;
    exports.resolveSSRNode = resolveSSRNode;
    exports.runHydrationEvents = runHydrationEvents;
    exports.setAttribute = setAttribute;
    exports.setAttributeNS = setAttributeNS;
    exports.spread = spread;
    exports.ssr = ssr;
    exports.ssrAttribute = ssrAttribute;
    exports.ssrClassList = ssrClassList;
    exports.ssrElement = ssrElement;
    exports.ssrHydrationKey = ssrHydrationKey;
    exports.ssrSpread = ssrSpread;
    exports.ssrStyle = ssrStyle;
    exports.style = style;
    exports.template = template;
    exports.use = use;
    exports.useAssets = voidFn;
  }
});

// src/ui/solid/plugin-context.tsx
var require_plugin_context = __commonJS({
  "src/ui/solid/plugin-context.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.PluginContextProvider = PluginContextProvider;
    exports.usePluginContext = usePluginContext;
    var _web = require_web();
    var _solidJs = require_solid();
    var _obsidian = require("obsidian");
    var PluginContext = (0, _solidJs.createContext)();
    function PluginContextProvider(props) {
      const handleClick = async (path, line) => {
        const file = props.plugin.app.metadataCache.getFirstLinkpathDest(path, path);
        if (!file) {
          new _obsidian.Notice(`File ${path} does not exist`);
          return;
        }
        await props.plugin.app.workspace.getLeaf(false).openFile(file);
        const activeMarkdownView = props.plugin.app.workspace.getActiveViewOfType(_obsidian.MarkdownView);
        if (!activeMarkdownView) {
          new _obsidian.Notice(`Failed to open file ${path}. Can't scroll to line ${line}`);
          return;
        }
        try {
          activeMarkdownView.setEphemeralState({
            line
          });
        } catch (error) {
          console.error(error);
        }
      };
      const handleMouseover = (event, path, line) => {
        if (!props.plugin.app.internalPlugins.plugins["page-preview"].enabled) {
          return;
        }
        if (_obsidian.Keymap.isModifier(event, "Mod")) {
          const target = event.target;
          const previewLocation = {
            scroll: line
          };
          if (path) {
            props.plugin.app.workspace.trigger("link-hover", {}, target, path, "", previewLocation);
          }
        }
      };
      const handleHeightChange = () => {
        props.infinityScroll.invalidateAll();
      };
      return (0, _web.createComponent)(PluginContext.Provider, {
        get value() {
          return {
            handleClick,
            handleMouseover,
            handleHeightChange,
            plugin: props.plugin
          };
        },
        get children() {
          return props.children;
        }
      });
    }
    function usePluginContext() {
      const pluginContext = (0, _solidJs.useContext)(PluginContext);
      if (!pluginContext) {
        throw new Error("pluginContext must be used inside a provider");
      }
      return pluginContext;
    }
  }
});

// src/ui/solid/icons/list-icon.tsx
var require_list_icon = __commonJS({
  "src/ui/solid/icons/list-icon.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.ListIcon = ListIcon;
    var _web = require_web();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="better-search-views-icon lucide lucide-list"><line x1="8" x2="21" y1="6" y2="6"></line><line x1="8" x2="21" y1="12" y2="12"></line><line x1="8" x2="21" y1="18" y2="18"></line><line x1="3" x2="3.01" y1="6" y2="6"></line><line x1="3" x2="3.01" y1="12" y2="12"></line><line x1="3" x2="3.01" y1="18" y2="18">`);
    function ListIcon() {
      return _tmpl$();
    }
  }
});

// src/ui/solid/icons/arrow-right-icon.tsx
var require_arrow_right_icon = __commonJS({
  "src/ui/solid/icons/arrow-right-icon.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.ArrowRightIcon = ArrowRightIcon;
    var _web = require_web();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="better-search-views-icon lucide lucide-corner-down-right"><polyline points="15 10 20 15 15 20"></polyline><path d="M4 4v7a4 4 0 0 0 4 4h12">`);
    function ArrowRightIcon() {
      return _tmpl$();
    }
  }
});

// src/ui/solid/icons/heading-icon.tsx
var require_heading_icon = __commonJS({
  "src/ui/solid/icons/heading-icon.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.HeadingIcon = HeadingIcon;
    var _web = require_web();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="better-search-views-icon lucide lucide-heading"><path d="M6 12h12"></path><path d="M6 20V4"></path><path d="M18 20V4">`);
    function HeadingIcon() {
      return _tmpl$();
    }
  }
});

// src/patterns.ts
var patterns_exports = {};
__export(patterns_exports, {
  listItemToken: () => listItemToken,
  wikiLinkBrackets: () => wikiLinkBrackets
});
var wikiLinkBrackets, listItemToken;
var init_patterns = __esm({
  "src/patterns.ts"() {
    wikiLinkBrackets = /\[\[|]]/g;
    listItemToken = /^-\s+/;
  }
});

// src/ui/solid/title.tsx
var require_title = __commonJS({
  "src/ui/solid/title.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.Title = Title;
    var _web = require_web();
    var _solidJs = require_solid();
    var _pluginContext = require_plugin_context();
    var _listIcon = require_list_icon();
    var _arrowRightIcon = require_arrow_right_icon();
    var _headingIcon = require_heading_icon();
    var _patterns = (init_patterns(), __toCommonJS(patterns_exports));
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<div class="better-search-views-titles-container">`);
    var _tmpl$2 = /* @__PURE__ */ (0, _web.template)(`<div class="tree-item-inner"><div class="better-search-views-breadcrumb-container"><div class="better-search-views-breadcrumb-token"></div><div>`);
    function removeListToken(text) {
      return text.trim().replace(_patterns.listItemToken, "");
    }
    function Title(props) {
      const {
        handleClick,
        handleMouseover
      } = (0, _pluginContext.usePluginContext)();
      return (() => {
        const _el$ = _tmpl$();
        (0, _web.insert)(_el$, (0, _web.createComponent)(_solidJs.For, {
          get each() {
            return props.breadcrumbs;
          },
          children: (breadcrumb, i) => {
            var _a;
            const handleTitleClick = async () => await handleClick(props.contextTree.filePath, breadcrumb.position.start.line);
            const handleTitleMouseover = (event) => handleMouseover(event, props.contextTree.filePath, breadcrumb.position.start.line);
            const previousIsHeading = ((_a = props.breadcrumbs[i() - 1]) == null ? void 0 : _a.type) === "heading";
            const isFirstListItemInBreadcrumbs = breadcrumb.type === "list" && (i() === 0 || previousIsHeading);
            const isFirstHeadingInBreadcrumbs = i() === 0 && breadcrumb.type === "heading";
            return (() => {
              const _el$2 = _tmpl$2(), _el$3 = _el$2.firstChild, _el$4 = _el$3.firstChild, _el$5 = _el$4.nextSibling;
              _el$3.$$mouseover = handleTitleMouseover;
              _el$3.$$click = handleTitleClick;
              (0, _web.insert)(_el$4, (0, _web.createComponent)(_solidJs.Switch, {
                get fallback() {
                  return (0, _web.createComponent)(_arrowRightIcon.ArrowRightIcon, {});
                },
                get children() {
                  return [(0, _web.createComponent)(_solidJs.Match, {
                    when: isFirstListItemInBreadcrumbs,
                    get children() {
                      return (0, _web.createComponent)(_listIcon.ListIcon, {});
                    }
                  }), (0, _web.createComponent)(_solidJs.Match, {
                    when: isFirstHeadingInBreadcrumbs,
                    get children() {
                      return (0, _web.createComponent)(_headingIcon.HeadingIcon, {});
                    }
                  })];
                }
              }));
              (0, _web.insert)(_el$5, () => removeListToken(breadcrumb.text));
              return _el$2;
            })();
          }
        }));
        return _el$;
      })();
    }
    (0, _web.delegateEvents)(["click", "mouseover"]);
  }
});

// src/ui/solid/match-section.tsx
var require_match_section = __commonJS({
  "src/ui/solid/match-section.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.MatchSection = MatchSection;
    var _web = require_web();
    var _solidJs = require_solid();
    var _obsidian = require("obsidian");
    var _pluginContext = require_plugin_context();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<div class="search-result-file-matches">`);
    var _tmpl$2 = /* @__PURE__ */ (0, _web.template)(`<div class="search-result-file-match better-search-views-file-match markdown-preview-view markdown-rendered">`);
    function MatchSection(props) {
      const {
        handleClick,
        handleMouseover
      } = (0, _pluginContext.usePluginContext)();
      const matchLifecycleManager = new _obsidian.Component();
      (0, _solidJs.onCleanup)(() => {
        matchLifecycleManager.unload();
      });
      return (0, _web.createComponent)(_solidJs.Show, {
        get when() {
          return props.sectionsWithMatches.length > 0;
        },
        get children() {
          const _el$ = _tmpl$();
          (0, _web.insert)(_el$, (0, _web.createComponent)(_solidJs.For, {
            get each() {
              return props.sectionsWithMatches;
            },
            children: (section) => {
              const line = section.cache.position.start.line;
              return (() => {
                const _el$2 = _tmpl$2();
                _el$2.$$mouseover = (event) => {
                  handleMouseover(event, section.filePath, line);
                };
                _el$2.$$click = async () => {
                  await handleClick(section.filePath, line);
                };
                (0, _web.use)(async (el) => {
                  await _obsidian.MarkdownRenderer.renderMarkdown(section.text, el, section.filePath, matchLifecycleManager);
                  matchLifecycleManager.load();
                }, _el$2);
                return _el$2;
              })();
            }
          }));
          return _el$;
        }
      });
    }
    (0, _web.delegateEvents)(["click", "mouseover"]);
  }
});

// src/ui/solid/icons/collapse-icon.tsx
var require_collapse_icon = __commonJS({
  "src/ui/solid/icons/collapse-icon.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.CollapseIcon = CollapseIcon;
    var _web = require_web();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon right-triangle"><path d="M3 8L12 17L21 8">`);
    function CollapseIcon() {
      return _tmpl$();
    }
  }
});

// src/ui/solid/branch.tsx
var require_branch = __commonJS({
  "src/ui/solid/branch.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.Branch = Branch;
    var _web = require_web();
    var _solidJs = require_solid();
    var _title = require_title();
    var _matchSection = require_match_section();
    var _collapseIcon = require_collapse_icon();
    var _pluginContext = require_plugin_context();
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<div class="tree-item-self search-result-file-title is-clickable"><div>`);
    var _tmpl$2 = /* @__PURE__ */ (0, _web.template)(`<div class="tree-item search-result better-search-views-tree"><div><div class="better-search-views-tree-item-children">`);
    function Branch(props) {
      const {
        handleHeightChange
      } = (0, _pluginContext.usePluginContext)();
      const [isHidden, setIsHidden] = (0, _solidJs.createSignal)(false);
      const breadcrumbs = () => {
        const breadcrumbForBranch = {
          text: props.contextTree.text,
          type: props.contextTree.type,
          position: props.contextTree.cacheItem.position
        };
        return [breadcrumbForBranch, ...props.contextTree.breadcrumbs || []];
      };
      return (() => {
        const _el$ = _tmpl$2(), _el$4 = _el$.firstChild, _el$5 = _el$4.firstChild;
        (0, _web.insert)(_el$, (0, _web.createComponent)(_solidJs.Show, {
          get when() {
            return props.contextTree.type !== "file";
          },
          get children() {
            const _el$2 = _tmpl$(), _el$3 = _el$2.firstChild;
            _el$3.$$click = () => {
              setIsHidden(!isHidden());
              handleHeightChange();
            };
            (0, _web.insert)(_el$3, (0, _web.createComponent)(_collapseIcon.CollapseIcon, {}));
            (0, _web.insert)(_el$2, (0, _web.createComponent)(_title.Title, {
              get breadcrumbs() {
                return breadcrumbs();
              },
              get contextTree() {
                return props.contextTree;
              }
            }), null);
            (0, _web.effect)(() => (0, _web.className)(_el$3, `tree-item-icon collapse-icon ${isHidden() ? "is-collapsed" : ""}`));
            return _el$2;
          }
        }), _el$4);
        (0, _web.insert)(_el$4, (0, _web.createComponent)(_matchSection.MatchSection, {
          get sectionsWithMatches() {
            return props.contextTree.sectionsWithMatches;
          }
        }), _el$5);
        (0, _web.insert)(_el$5, (0, _web.createComponent)(_solidJs.For, {
          get each() {
            return props.contextTree.branches;
          },
          children: (branch) => (0, _web.createComponent)(Branch, {
            contextTree: branch
          })
        }));
        (0, _web.effect)(() => (0, _web.className)(_el$4, isHidden() ? "better-search-views-is-hidden" : ""));
        return _el$;
      })();
    }
    (0, _web.delegateEvents)(["click"]);
  }
});

// src/context-tree/collapse/collapse-empty-nodes.ts
var collapse_empty_nodes_exports = {};
__export(collapse_empty_nodes_exports, {
  collapseEmptyNodes: () => collapseEmptyNodes
});
function collapseEmptyNodes(tree) {
  var _a, _b;
  if (!((_a = tree == null ? void 0 : tree.sectionsWithMatches) == null ? void 0 : _a.length) && tree.branches.length === 1) {
    const firstBranch = tree.branches[0];
    const breadcrumb = {
      text: firstBranch.text,
      type: firstBranch.type,
      position: (_b = firstBranch.cacheItem) == null ? void 0 : _b.position
    };
    return collapseEmptyNodes({
      ...tree,
      branches: firstBranch.branches,
      breadcrumbs: [...tree.breadcrumbs || [], breadcrumb],
      sectionsWithMatches: firstBranch.sectionsWithMatches
    });
  }
  return { ...tree, branches: tree.branches.map(collapseEmptyNodes) };
}
var init_collapse_empty_nodes = __esm({
  "src/context-tree/collapse/collapse-empty-nodes.ts"() {
  }
});

// node_modules/mark.js/dist/mark.js
var require_mark = __commonJS({
  "node_modules/mark.js/dist/mark.js"(exports, module2) {
    (function(global, factory) {
      typeof exports === "object" && typeof module2 !== "undefined" ? module2.exports = factory() : typeof define === "function" && define.amd ? define(factory) : global.Mark = factory();
    })(exports, function() {
      "use strict";
      var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
        return typeof obj;
      } : function(obj) {
        return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
      };
      var classCallCheck = function(instance, Constructor) {
        if (!(instance instanceof Constructor)) {
          throw new TypeError("Cannot call a class as a function");
        }
      };
      var createClass = function() {
        function defineProperties(target, props) {
          for (var i = 0; i < props.length; i++) {
            var descriptor = props[i];
            descriptor.enumerable = descriptor.enumerable || false;
            descriptor.configurable = true;
            if ("value" in descriptor)
              descriptor.writable = true;
            Object.defineProperty(target, descriptor.key, descriptor);
          }
        }
        return function(Constructor, protoProps, staticProps) {
          if (protoProps)
            defineProperties(Constructor.prototype, protoProps);
          if (staticProps)
            defineProperties(Constructor, staticProps);
          return Constructor;
        };
      }();
      var _extends = Object.assign || function(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i];
          for (var key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
              target[key] = source[key];
            }
          }
        }
        return target;
      };
      var DOMIterator = function() {
        function DOMIterator2(ctx) {
          var iframes = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
          var exclude = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];
          var iframesTimeout = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 5e3;
          classCallCheck(this, DOMIterator2);
          this.ctx = ctx;
          this.iframes = iframes;
          this.exclude = exclude;
          this.iframesTimeout = iframesTimeout;
        }
        createClass(DOMIterator2, [{
          key: "getContexts",
          value: function getContexts() {
            var ctx = void 0, filteredCtx = [];
            if (typeof this.ctx === "undefined" || !this.ctx) {
              ctx = [];
            } else if (NodeList.prototype.isPrototypeOf(this.ctx)) {
              ctx = Array.prototype.slice.call(this.ctx);
            } else if (Array.isArray(this.ctx)) {
              ctx = this.ctx;
            } else if (typeof this.ctx === "string") {
              ctx = Array.prototype.slice.call(document.querySelectorAll(this.ctx));
            } else {
              ctx = [this.ctx];
            }
            ctx.forEach(function(ctx2) {
              var isDescendant = filteredCtx.filter(function(contexts) {
                return contexts.contains(ctx2);
              }).length > 0;
              if (filteredCtx.indexOf(ctx2) === -1 && !isDescendant) {
                filteredCtx.push(ctx2);
              }
            });
            return filteredCtx;
          }
        }, {
          key: "getIframeContents",
          value: function getIframeContents(ifr, successFn) {
            var errorFn = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : function() {
            };
            var doc = void 0;
            try {
              var ifrWin = ifr.contentWindow;
              doc = ifrWin.document;
              if (!ifrWin || !doc) {
                throw new Error("iframe inaccessible");
              }
            } catch (e) {
              errorFn();
            }
            if (doc) {
              successFn(doc);
            }
          }
        }, {
          key: "isIframeBlank",
          value: function isIframeBlank(ifr) {
            var bl = "about:blank", src = ifr.getAttribute("src").trim(), href = ifr.contentWindow.location.href;
            return href === bl && src !== bl && src;
          }
        }, {
          key: "observeIframeLoad",
          value: function observeIframeLoad(ifr, successFn, errorFn) {
            var _this = this;
            var called = false, tout = null;
            var listener = function listener2() {
              if (called) {
                return;
              }
              called = true;
              clearTimeout(tout);
              try {
                if (!_this.isIframeBlank(ifr)) {
                  ifr.removeEventListener("load", listener2);
                  _this.getIframeContents(ifr, successFn, errorFn);
                }
              } catch (e) {
                errorFn();
              }
            };
            ifr.addEventListener("load", listener);
            tout = setTimeout(listener, this.iframesTimeout);
          }
        }, {
          key: "onIframeReady",
          value: function onIframeReady(ifr, successFn, errorFn) {
            try {
              if (ifr.contentWindow.document.readyState === "complete") {
                if (this.isIframeBlank(ifr)) {
                  this.observeIframeLoad(ifr, successFn, errorFn);
                } else {
                  this.getIframeContents(ifr, successFn, errorFn);
                }
              } else {
                this.observeIframeLoad(ifr, successFn, errorFn);
              }
            } catch (e) {
              errorFn();
            }
          }
        }, {
          key: "waitForIframes",
          value: function waitForIframes(ctx, done) {
            var _this2 = this;
            var eachCalled = 0;
            this.forEachIframe(ctx, function() {
              return true;
            }, function(ifr) {
              eachCalled++;
              _this2.waitForIframes(ifr.querySelector("html"), function() {
                if (!--eachCalled) {
                  done();
                }
              });
            }, function(handled) {
              if (!handled) {
                done();
              }
            });
          }
        }, {
          key: "forEachIframe",
          value: function forEachIframe(ctx, filter, each) {
            var _this3 = this;
            var end = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : function() {
            };
            var ifr = ctx.querySelectorAll("iframe"), open = ifr.length, handled = 0;
            ifr = Array.prototype.slice.call(ifr);
            var checkEnd = function checkEnd2() {
              if (--open <= 0) {
                end(handled);
              }
            };
            if (!open) {
              checkEnd();
            }
            ifr.forEach(function(ifr2) {
              if (DOMIterator2.matches(ifr2, _this3.exclude)) {
                checkEnd();
              } else {
                _this3.onIframeReady(ifr2, function(con) {
                  if (filter(ifr2)) {
                    handled++;
                    each(con);
                  }
                  checkEnd();
                }, checkEnd);
              }
            });
          }
        }, {
          key: "createIterator",
          value: function createIterator(ctx, whatToShow, filter) {
            return document.createNodeIterator(ctx, whatToShow, filter, false);
          }
        }, {
          key: "createInstanceOnIframe",
          value: function createInstanceOnIframe(contents) {
            return new DOMIterator2(contents.querySelector("html"), this.iframes);
          }
        }, {
          key: "compareNodeIframe",
          value: function compareNodeIframe(node, prevNode, ifr) {
            var compCurr = node.compareDocumentPosition(ifr), prev = Node.DOCUMENT_POSITION_PRECEDING;
            if (compCurr & prev) {
              if (prevNode !== null) {
                var compPrev = prevNode.compareDocumentPosition(ifr), after = Node.DOCUMENT_POSITION_FOLLOWING;
                if (compPrev & after) {
                  return true;
                }
              } else {
                return true;
              }
            }
            return false;
          }
        }, {
          key: "getIteratorNode",
          value: function getIteratorNode(itr) {
            var prevNode = itr.previousNode();
            var node = void 0;
            if (prevNode === null) {
              node = itr.nextNode();
            } else {
              node = itr.nextNode() && itr.nextNode();
            }
            return {
              prevNode,
              node
            };
          }
        }, {
          key: "checkIframeFilter",
          value: function checkIframeFilter(node, prevNode, currIfr, ifr) {
            var key = false, handled = false;
            ifr.forEach(function(ifrDict, i) {
              if (ifrDict.val === currIfr) {
                key = i;
                handled = ifrDict.handled;
              }
            });
            if (this.compareNodeIframe(node, prevNode, currIfr)) {
              if (key === false && !handled) {
                ifr.push({
                  val: currIfr,
                  handled: true
                });
              } else if (key !== false && !handled) {
                ifr[key].handled = true;
              }
              return true;
            }
            if (key === false) {
              ifr.push({
                val: currIfr,
                handled: false
              });
            }
            return false;
          }
        }, {
          key: "handleOpenIframes",
          value: function handleOpenIframes(ifr, whatToShow, eCb, fCb) {
            var _this4 = this;
            ifr.forEach(function(ifrDict) {
              if (!ifrDict.handled) {
                _this4.getIframeContents(ifrDict.val, function(con) {
                  _this4.createInstanceOnIframe(con).forEachNode(whatToShow, eCb, fCb);
                });
              }
            });
          }
        }, {
          key: "iterateThroughNodes",
          value: function iterateThroughNodes(whatToShow, ctx, eachCb, filterCb, doneCb) {
            var _this5 = this;
            var itr = this.createIterator(ctx, whatToShow, filterCb);
            var ifr = [], elements = [], node = void 0, prevNode = void 0, retrieveNodes = function retrieveNodes2() {
              var _getIteratorNode = _this5.getIteratorNode(itr);
              prevNode = _getIteratorNode.prevNode;
              node = _getIteratorNode.node;
              return node;
            };
            while (retrieveNodes()) {
              if (this.iframes) {
                this.forEachIframe(ctx, function(currIfr) {
                  return _this5.checkIframeFilter(node, prevNode, currIfr, ifr);
                }, function(con) {
                  _this5.createInstanceOnIframe(con).forEachNode(whatToShow, function(ifrNode) {
                    return elements.push(ifrNode);
                  }, filterCb);
                });
              }
              elements.push(node);
            }
            elements.forEach(function(node2) {
              eachCb(node2);
            });
            if (this.iframes) {
              this.handleOpenIframes(ifr, whatToShow, eachCb, filterCb);
            }
            doneCb();
          }
        }, {
          key: "forEachNode",
          value: function forEachNode(whatToShow, each, filter) {
            var _this6 = this;
            var done = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : function() {
            };
            var contexts = this.getContexts();
            var open = contexts.length;
            if (!open) {
              done();
            }
            contexts.forEach(function(ctx) {
              var ready = function ready2() {
                _this6.iterateThroughNodes(whatToShow, ctx, each, filter, function() {
                  if (--open <= 0) {
                    done();
                  }
                });
              };
              if (_this6.iframes) {
                _this6.waitForIframes(ctx, ready);
              } else {
                ready();
              }
            });
          }
        }], [{
          key: "matches",
          value: function matches(element, selector) {
            var selectors = typeof selector === "string" ? [selector] : selector, fn = element.matches || element.matchesSelector || element.msMatchesSelector || element.mozMatchesSelector || element.oMatchesSelector || element.webkitMatchesSelector;
            if (fn) {
              var match = false;
              selectors.every(function(sel) {
                if (fn.call(element, sel)) {
                  match = true;
                  return false;
                }
                return true;
              });
              return match;
            } else {
              return false;
            }
          }
        }]);
        return DOMIterator2;
      }();
      var Mark$1 = function() {
        function Mark2(ctx) {
          classCallCheck(this, Mark2);
          this.ctx = ctx;
          this.ie = false;
          var ua = window.navigator.userAgent;
          if (ua.indexOf("MSIE") > -1 || ua.indexOf("Trident") > -1) {
            this.ie = true;
          }
        }
        createClass(Mark2, [{
          key: "log",
          value: function log(msg) {
            var level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "debug";
            var log2 = this.opt.log;
            if (!this.opt.debug) {
              return;
            }
            if ((typeof log2 === "undefined" ? "undefined" : _typeof(log2)) === "object" && typeof log2[level] === "function") {
              log2[level]("mark.js: " + msg);
            }
          }
        }, {
          key: "escapeStr",
          value: function escapeStr(str) {
            return str.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");
          }
        }, {
          key: "createRegExp",
          value: function createRegExp(str) {
            if (this.opt.wildcards !== "disabled") {
              str = this.setupWildcardsRegExp(str);
            }
            str = this.escapeStr(str);
            if (Object.keys(this.opt.synonyms).length) {
              str = this.createSynonymsRegExp(str);
            }
            if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {
              str = this.setupIgnoreJoinersRegExp(str);
            }
            if (this.opt.diacritics) {
              str = this.createDiacriticsRegExp(str);
            }
            str = this.createMergedBlanksRegExp(str);
            if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {
              str = this.createJoinersRegExp(str);
            }
            if (this.opt.wildcards !== "disabled") {
              str = this.createWildcardsRegExp(str);
            }
            str = this.createAccuracyRegExp(str);
            return str;
          }
        }, {
          key: "createSynonymsRegExp",
          value: function createSynonymsRegExp(str) {
            var syn = this.opt.synonyms, sens = this.opt.caseSensitive ? "" : "i", joinerPlaceholder = this.opt.ignoreJoiners || this.opt.ignorePunctuation.length ? "\0" : "";
            for (var index in syn) {
              if (syn.hasOwnProperty(index)) {
                var value = syn[index], k1 = this.opt.wildcards !== "disabled" ? this.setupWildcardsRegExp(index) : this.escapeStr(index), k2 = this.opt.wildcards !== "disabled" ? this.setupWildcardsRegExp(value) : this.escapeStr(value);
                if (k1 !== "" && k2 !== "") {
                  str = str.replace(new RegExp("(" + this.escapeStr(k1) + "|" + this.escapeStr(k2) + ")", "gm" + sens), joinerPlaceholder + ("(" + this.processSynomyms(k1) + "|") + (this.processSynomyms(k2) + ")") + joinerPlaceholder);
                }
              }
            }
            return str;
          }
        }, {
          key: "processSynomyms",
          value: function processSynomyms(str) {
            if (this.opt.ignoreJoiners || this.opt.ignorePunctuation.length) {
              str = this.setupIgnoreJoinersRegExp(str);
            }
            return str;
          }
        }, {
          key: "setupWildcardsRegExp",
          value: function setupWildcardsRegExp(str) {
            str = str.replace(/(?:\\)*\?/g, function(val) {
              return val.charAt(0) === "\\" ? "?" : "";
            });
            return str.replace(/(?:\\)*\*/g, function(val) {
              return val.charAt(0) === "\\" ? "*" : "";
            });
          }
        }, {
          key: "createWildcardsRegExp",
          value: function createWildcardsRegExp(str) {
            var spaces = this.opt.wildcards === "withSpaces";
            return str.replace(/\u0001/g, spaces ? "[\\S\\s]?" : "\\S?").replace(/\u0002/g, spaces ? "[\\S\\s]*?" : "\\S*");
          }
        }, {
          key: "setupIgnoreJoinersRegExp",
          value: function setupIgnoreJoinersRegExp(str) {
            return str.replace(/[^(|)\\]/g, function(val, indx, original) {
              var nextChar = original.charAt(indx + 1);
              if (/[(|)\\]/.test(nextChar) || nextChar === "") {
                return val;
              } else {
                return val + "\0";
              }
            });
          }
        }, {
          key: "createJoinersRegExp",
          value: function createJoinersRegExp(str) {
            var joiner = [];
            var ignorePunctuation = this.opt.ignorePunctuation;
            if (Array.isArray(ignorePunctuation) && ignorePunctuation.length) {
              joiner.push(this.escapeStr(ignorePunctuation.join("")));
            }
            if (this.opt.ignoreJoiners) {
              joiner.push("\\u00ad\\u200b\\u200c\\u200d");
            }
            return joiner.length ? str.split(/\u0000+/).join("[" + joiner.join("") + "]*") : str;
          }
        }, {
          key: "createDiacriticsRegExp",
          value: function createDiacriticsRegExp(str) {
            var sens = this.opt.caseSensitive ? "" : "i", dct = this.opt.caseSensitive ? ["a\xE0\xE1\u1EA3\xE3\u1EA1\u0103\u1EB1\u1EAF\u1EB3\u1EB5\u1EB7\xE2\u1EA7\u1EA5\u1EA9\u1EAB\u1EAD\xE4\xE5\u0101\u0105", "A\xC0\xC1\u1EA2\xC3\u1EA0\u0102\u1EB0\u1EAE\u1EB2\u1EB4\u1EB6\xC2\u1EA6\u1EA4\u1EA8\u1EAA\u1EAC\xC4\xC5\u0100\u0104", "c\xE7\u0107\u010D", "C\xC7\u0106\u010C", "d\u0111\u010F", "D\u0110\u010E", "e\xE8\xE9\u1EBB\u1EBD\u1EB9\xEA\u1EC1\u1EBF\u1EC3\u1EC5\u1EC7\xEB\u011B\u0113\u0119", "E\xC8\xC9\u1EBA\u1EBC\u1EB8\xCA\u1EC0\u1EBE\u1EC2\u1EC4\u1EC6\xCB\u011A\u0112\u0118", "i\xEC\xED\u1EC9\u0129\u1ECB\xEE\xEF\u012B", "I\xCC\xCD\u1EC8\u0128\u1ECA\xCE\xCF\u012A", "l\u0142", "L\u0141", "n\xF1\u0148\u0144", "N\xD1\u0147\u0143", "o\xF2\xF3\u1ECF\xF5\u1ECD\xF4\u1ED3\u1ED1\u1ED5\u1ED7\u1ED9\u01A1\u1EDF\u1EE1\u1EDB\u1EDD\u1EE3\xF6\xF8\u014D", "O\xD2\xD3\u1ECE\xD5\u1ECC\xD4\u1ED2\u1ED0\u1ED4\u1ED6\u1ED8\u01A0\u1EDE\u1EE0\u1EDA\u1EDC\u1EE2\xD6\xD8\u014C", "r\u0159", "R\u0158", "s\u0161\u015B\u0219\u015F", "S\u0160\u015A\u0218\u015E", "t\u0165\u021B\u0163", "T\u0164\u021A\u0162", "u\xF9\xFA\u1EE7\u0169\u1EE5\u01B0\u1EEB\u1EE9\u1EED\u1EEF\u1EF1\xFB\xFC\u016F\u016B", "U\xD9\xDA\u1EE6\u0168\u1EE4\u01AF\u1EEA\u1EE8\u1EEC\u1EEE\u1EF0\xDB\xDC\u016E\u016A", "y\xFD\u1EF3\u1EF7\u1EF9\u1EF5\xFF", "Y\xDD\u1EF2\u1EF6\u1EF8\u1EF4\u0178", "z\u017E\u017C\u017A", "Z\u017D\u017B\u0179"] : ["a\xE0\xE1\u1EA3\xE3\u1EA1\u0103\u1EB1\u1EAF\u1EB3\u1EB5\u1EB7\xE2\u1EA7\u1EA5\u1EA9\u1EAB\u1EAD\xE4\xE5\u0101\u0105A\xC0\xC1\u1EA2\xC3\u1EA0\u0102\u1EB0\u1EAE\u1EB2\u1EB4\u1EB6\xC2\u1EA6\u1EA4\u1EA8\u1EAA\u1EAC\xC4\xC5\u0100\u0104", "c\xE7\u0107\u010DC\xC7\u0106\u010C", "d\u0111\u010FD\u0110\u010E", "e\xE8\xE9\u1EBB\u1EBD\u1EB9\xEA\u1EC1\u1EBF\u1EC3\u1EC5\u1EC7\xEB\u011B\u0113\u0119E\xC8\xC9\u1EBA\u1EBC\u1EB8\xCA\u1EC0\u1EBE\u1EC2\u1EC4\u1EC6\xCB\u011A\u0112\u0118", "i\xEC\xED\u1EC9\u0129\u1ECB\xEE\xEF\u012BI\xCC\xCD\u1EC8\u0128\u1ECA\xCE\xCF\u012A", "l\u0142L\u0141", "n\xF1\u0148\u0144N\xD1\u0147\u0143", "o\xF2\xF3\u1ECF\xF5\u1ECD\xF4\u1ED3\u1ED1\u1ED5\u1ED7\u1ED9\u01A1\u1EDF\u1EE1\u1EDB\u1EDD\u1EE3\xF6\xF8\u014DO\xD2\xD3\u1ECE\xD5\u1ECC\xD4\u1ED2\u1ED0\u1ED4\u1ED6\u1ED8\u01A0\u1EDE\u1EE0\u1EDA\u1EDC\u1EE2\xD6\xD8\u014C", "r\u0159R\u0158", "s\u0161\u015B\u0219\u015FS\u0160\u015A\u0218\u015E", "t\u0165\u021B\u0163T\u0164\u021A\u0162", "u\xF9\xFA\u1EE7\u0169\u1EE5\u01B0\u1EEB\u1EE9\u1EED\u1EEF\u1EF1\xFB\xFC\u016F\u016BU\xD9\xDA\u1EE6\u0168\u1EE4\u01AF\u1EEA\u1EE8\u1EEC\u1EEE\u1EF0\xDB\xDC\u016E\u016A", "y\xFD\u1EF3\u1EF7\u1EF9\u1EF5\xFFY\xDD\u1EF2\u1EF6\u1EF8\u1EF4\u0178", "z\u017E\u017C\u017AZ\u017D\u017B\u0179"];
            var handled = [];
            str.split("").forEach(function(ch) {
              dct.every(function(dct2) {
                if (dct2.indexOf(ch) !== -1) {
                  if (handled.indexOf(dct2) > -1) {
                    return false;
                  }
                  str = str.replace(new RegExp("[" + dct2 + "]", "gm" + sens), "[" + dct2 + "]");
                  handled.push(dct2);
                }
                return true;
              });
            });
            return str;
          }
        }, {
          key: "createMergedBlanksRegExp",
          value: function createMergedBlanksRegExp(str) {
            return str.replace(/[\s]+/gmi, "[\\s]+");
          }
        }, {
          key: "createAccuracyRegExp",
          value: function createAccuracyRegExp(str) {
            var _this = this;
            var chars = "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~\xA1\xBF";
            var acc = this.opt.accuracy, val = typeof acc === "string" ? acc : acc.value, ls = typeof acc === "string" ? [] : acc.limiters, lsJoin = "";
            ls.forEach(function(limiter) {
              lsJoin += "|" + _this.escapeStr(limiter);
            });
            switch (val) {
              case "partially":
              default:
                return "()(" + str + ")";
              case "complementary":
                lsJoin = "\\s" + (lsJoin ? lsJoin : this.escapeStr(chars));
                return "()([^" + lsJoin + "]*" + str + "[^" + lsJoin + "]*)";
              case "exactly":
                return "(^|\\s" + lsJoin + ")(" + str + ")(?=$|\\s" + lsJoin + ")";
            }
          }
        }, {
          key: "getSeparatedKeywords",
          value: function getSeparatedKeywords(sv) {
            var _this2 = this;
            var stack = [];
            sv.forEach(function(kw) {
              if (!_this2.opt.separateWordSearch) {
                if (kw.trim() && stack.indexOf(kw) === -1) {
                  stack.push(kw);
                }
              } else {
                kw.split(" ").forEach(function(kwSplitted) {
                  if (kwSplitted.trim() && stack.indexOf(kwSplitted) === -1) {
                    stack.push(kwSplitted);
                  }
                });
              }
            });
            return {
              "keywords": stack.sort(function(a, b) {
                return b.length - a.length;
              }),
              "length": stack.length
            };
          }
        }, {
          key: "isNumeric",
          value: function isNumeric(value) {
            return Number(parseFloat(value)) == value;
          }
        }, {
          key: "checkRanges",
          value: function checkRanges(array) {
            var _this3 = this;
            if (!Array.isArray(array) || Object.prototype.toString.call(array[0]) !== "[object Object]") {
              this.log("markRanges() will only accept an array of objects");
              this.opt.noMatch(array);
              return [];
            }
            var stack = [];
            var last = 0;
            array.sort(function(a, b) {
              return a.start - b.start;
            }).forEach(function(item) {
              var _callNoMatchOnInvalid = _this3.callNoMatchOnInvalidRanges(item, last), start = _callNoMatchOnInvalid.start, end = _callNoMatchOnInvalid.end, valid = _callNoMatchOnInvalid.valid;
              if (valid) {
                item.start = start;
                item.length = end - start;
                stack.push(item);
                last = end;
              }
            });
            return stack;
          }
        }, {
          key: "callNoMatchOnInvalidRanges",
          value: function callNoMatchOnInvalidRanges(range, last) {
            var start = void 0, end = void 0, valid = false;
            if (range && typeof range.start !== "undefined") {
              start = parseInt(range.start, 10);
              end = start + parseInt(range.length, 10);
              if (this.isNumeric(range.start) && this.isNumeric(range.length) && end - last > 0 && end - start > 0) {
                valid = true;
              } else {
                this.log("Ignoring invalid or overlapping range: " + ("" + JSON.stringify(range)));
                this.opt.noMatch(range);
              }
            } else {
              this.log("Ignoring invalid range: " + JSON.stringify(range));
              this.opt.noMatch(range);
            }
            return {
              start,
              end,
              valid
            };
          }
        }, {
          key: "checkWhitespaceRanges",
          value: function checkWhitespaceRanges(range, originalLength, string) {
            var end = void 0, valid = true, max = string.length, offset = originalLength - max, start = parseInt(range.start, 10) - offset;
            start = start > max ? max : start;
            end = start + parseInt(range.length, 10);
            if (end > max) {
              end = max;
              this.log("End range automatically set to the max value of " + max);
            }
            if (start < 0 || end - start < 0 || start > max || end > max) {
              valid = false;
              this.log("Invalid range: " + JSON.stringify(range));
              this.opt.noMatch(range);
            } else if (string.substring(start, end).replace(/\s+/g, "") === "") {
              valid = false;
              this.log("Skipping whitespace only range: " + JSON.stringify(range));
              this.opt.noMatch(range);
            }
            return {
              start,
              end,
              valid
            };
          }
        }, {
          key: "getTextNodes",
          value: function getTextNodes(cb) {
            var _this4 = this;
            var val = "", nodes = [];
            this.iterator.forEachNode(NodeFilter.SHOW_TEXT, function(node) {
              nodes.push({
                start: val.length,
                end: (val += node.textContent).length,
                node
              });
            }, function(node) {
              if (_this4.matchesExclude(node.parentNode)) {
                return NodeFilter.FILTER_REJECT;
              } else {
                return NodeFilter.FILTER_ACCEPT;
              }
            }, function() {
              cb({
                value: val,
                nodes
              });
            });
          }
        }, {
          key: "matchesExclude",
          value: function matchesExclude(el) {
            return DOMIterator.matches(el, this.opt.exclude.concat(["script", "style", "title", "head", "html"]));
          }
        }, {
          key: "wrapRangeInTextNode",
          value: function wrapRangeInTextNode(node, start, end) {
            var hEl = !this.opt.element ? "mark" : this.opt.element, startNode = node.splitText(start), ret = startNode.splitText(end - start);
            var repl = document.createElement(hEl);
            repl.setAttribute("data-markjs", "true");
            if (this.opt.className) {
              repl.setAttribute("class", this.opt.className);
            }
            repl.textContent = startNode.textContent;
            startNode.parentNode.replaceChild(repl, startNode);
            return ret;
          }
        }, {
          key: "wrapRangeInMappedTextNode",
          value: function wrapRangeInMappedTextNode(dict, start, end, filterCb, eachCb) {
            var _this5 = this;
            dict.nodes.every(function(n, i) {
              var sibl = dict.nodes[i + 1];
              if (typeof sibl === "undefined" || sibl.start > start) {
                if (!filterCb(n.node)) {
                  return false;
                }
                var s = start - n.start, e = (end > n.end ? n.end : end) - n.start, startStr = dict.value.substr(0, n.start), endStr = dict.value.substr(e + n.start);
                n.node = _this5.wrapRangeInTextNode(n.node, s, e);
                dict.value = startStr + endStr;
                dict.nodes.forEach(function(k, j) {
                  if (j >= i) {
                    if (dict.nodes[j].start > 0 && j !== i) {
                      dict.nodes[j].start -= e;
                    }
                    dict.nodes[j].end -= e;
                  }
                });
                end -= e;
                eachCb(n.node.previousSibling, n.start);
                if (end > n.end) {
                  start = n.end;
                } else {
                  return false;
                }
              }
              return true;
            });
          }
        }, {
          key: "wrapMatches",
          value: function wrapMatches(regex, ignoreGroups, filterCb, eachCb, endCb) {
            var _this6 = this;
            var matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;
            this.getTextNodes(function(dict) {
              dict.nodes.forEach(function(node) {
                node = node.node;
                var match = void 0;
                while ((match = regex.exec(node.textContent)) !== null && match[matchIdx] !== "") {
                  if (!filterCb(match[matchIdx], node)) {
                    continue;
                  }
                  var pos = match.index;
                  if (matchIdx !== 0) {
                    for (var i = 1; i < matchIdx; i++) {
                      pos += match[i].length;
                    }
                  }
                  node = _this6.wrapRangeInTextNode(node, pos, pos + match[matchIdx].length);
                  eachCb(node.previousSibling);
                  regex.lastIndex = 0;
                }
              });
              endCb();
            });
          }
        }, {
          key: "wrapMatchesAcrossElements",
          value: function wrapMatchesAcrossElements(regex, ignoreGroups, filterCb, eachCb, endCb) {
            var _this7 = this;
            var matchIdx = ignoreGroups === 0 ? 0 : ignoreGroups + 1;
            this.getTextNodes(function(dict) {
              var match = void 0;
              while ((match = regex.exec(dict.value)) !== null && match[matchIdx] !== "") {
                var start = match.index;
                if (matchIdx !== 0) {
                  for (var i = 1; i < matchIdx; i++) {
                    start += match[i].length;
                  }
                }
                var end = start + match[matchIdx].length;
                _this7.wrapRangeInMappedTextNode(dict, start, end, function(node) {
                  return filterCb(match[matchIdx], node);
                }, function(node, lastIndex) {
                  regex.lastIndex = lastIndex;
                  eachCb(node);
                });
              }
              endCb();
            });
          }
        }, {
          key: "wrapRangeFromIndex",
          value: function wrapRangeFromIndex(ranges, filterCb, eachCb, endCb) {
            var _this8 = this;
            this.getTextNodes(function(dict) {
              var originalLength = dict.value.length;
              ranges.forEach(function(range, counter) {
                var _checkWhitespaceRange = _this8.checkWhitespaceRanges(range, originalLength, dict.value), start = _checkWhitespaceRange.start, end = _checkWhitespaceRange.end, valid = _checkWhitespaceRange.valid;
                if (valid) {
                  _this8.wrapRangeInMappedTextNode(dict, start, end, function(node) {
                    return filterCb(node, range, dict.value.substring(start, end), counter);
                  }, function(node) {
                    eachCb(node, range);
                  });
                }
              });
              endCb();
            });
          }
        }, {
          key: "unwrapMatches",
          value: function unwrapMatches(node) {
            var parent = node.parentNode;
            var docFrag = document.createDocumentFragment();
            while (node.firstChild) {
              docFrag.appendChild(node.removeChild(node.firstChild));
            }
            parent.replaceChild(docFrag, node);
            if (!this.ie) {
              parent.normalize();
            } else {
              this.normalizeTextNode(parent);
            }
          }
        }, {
          key: "normalizeTextNode",
          value: function normalizeTextNode(node) {
            if (!node) {
              return;
            }
            if (node.nodeType === 3) {
              while (node.nextSibling && node.nextSibling.nodeType === 3) {
                node.nodeValue += node.nextSibling.nodeValue;
                node.parentNode.removeChild(node.nextSibling);
              }
            } else {
              this.normalizeTextNode(node.firstChild);
            }
            this.normalizeTextNode(node.nextSibling);
          }
        }, {
          key: "markRegExp",
          value: function markRegExp(regexp, opt) {
            var _this9 = this;
            this.opt = opt;
            this.log('Searching with expression "' + regexp + '"');
            var totalMatches = 0, fn = "wrapMatches";
            var eachCb = function eachCb2(element) {
              totalMatches++;
              _this9.opt.each(element);
            };
            if (this.opt.acrossElements) {
              fn = "wrapMatchesAcrossElements";
            }
            this[fn](regexp, this.opt.ignoreGroups, function(match, node) {
              return _this9.opt.filter(node, match, totalMatches);
            }, eachCb, function() {
              if (totalMatches === 0) {
                _this9.opt.noMatch(regexp);
              }
              _this9.opt.done(totalMatches);
            });
          }
        }, {
          key: "mark",
          value: function mark(sv, opt) {
            var _this10 = this;
            this.opt = opt;
            var totalMatches = 0, fn = "wrapMatches";
            var _getSeparatedKeywords = this.getSeparatedKeywords(typeof sv === "string" ? [sv] : sv), kwArr = _getSeparatedKeywords.keywords, kwArrLen = _getSeparatedKeywords.length, sens = this.opt.caseSensitive ? "" : "i", handler = function handler2(kw) {
              var regex = new RegExp(_this10.createRegExp(kw), "gm" + sens), matches = 0;
              _this10.log('Searching with expression "' + regex + '"');
              _this10[fn](regex, 1, function(term, node) {
                return _this10.opt.filter(node, kw, totalMatches, matches);
              }, function(element) {
                matches++;
                totalMatches++;
                _this10.opt.each(element);
              }, function() {
                if (matches === 0) {
                  _this10.opt.noMatch(kw);
                }
                if (kwArr[kwArrLen - 1] === kw) {
                  _this10.opt.done(totalMatches);
                } else {
                  handler2(kwArr[kwArr.indexOf(kw) + 1]);
                }
              });
            };
            if (this.opt.acrossElements) {
              fn = "wrapMatchesAcrossElements";
            }
            if (kwArrLen === 0) {
              this.opt.done(totalMatches);
            } else {
              handler(kwArr[0]);
            }
          }
        }, {
          key: "markRanges",
          value: function markRanges(rawRanges, opt) {
            var _this11 = this;
            this.opt = opt;
            var totalMatches = 0, ranges = this.checkRanges(rawRanges);
            if (ranges && ranges.length) {
              this.log("Starting to mark with the following ranges: " + JSON.stringify(ranges));
              this.wrapRangeFromIndex(ranges, function(node, range, match, counter) {
                return _this11.opt.filter(node, range, match, counter);
              }, function(element, range) {
                totalMatches++;
                _this11.opt.each(element, range);
              }, function() {
                _this11.opt.done(totalMatches);
              });
            } else {
              this.opt.done(totalMatches);
            }
          }
        }, {
          key: "unmark",
          value: function unmark(opt) {
            var _this12 = this;
            this.opt = opt;
            var sel = this.opt.element ? this.opt.element : "*";
            sel += "[data-markjs]";
            if (this.opt.className) {
              sel += "." + this.opt.className;
            }
            this.log('Removal selector "' + sel + '"');
            this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT, function(node) {
              _this12.unwrapMatches(node);
            }, function(node) {
              var matchesSel = DOMIterator.matches(node, sel), matchesExclude = _this12.matchesExclude(node);
              if (!matchesSel || matchesExclude) {
                return NodeFilter.FILTER_REJECT;
              } else {
                return NodeFilter.FILTER_ACCEPT;
              }
            }, this.opt.done);
          }
        }, {
          key: "opt",
          set: function set$$1(val) {
            this._opt = _extends({}, {
              "element": "",
              "className": "",
              "exclude": [],
              "iframes": false,
              "iframesTimeout": 5e3,
              "separateWordSearch": true,
              "diacritics": true,
              "synonyms": {},
              "accuracy": "partially",
              "acrossElements": false,
              "caseSensitive": false,
              "ignoreJoiners": false,
              "ignoreGroups": 0,
              "ignorePunctuation": [],
              "wildcards": "disabled",
              "each": function each() {
              },
              "noMatch": function noMatch() {
              },
              "filter": function filter() {
                return true;
              },
              "done": function done() {
              },
              "debug": false,
              "log": window.console
            }, val);
          },
          get: function get$$1() {
            return this._opt;
          }
        }, {
          key: "iterator",
          get: function get$$1() {
            return new DOMIterator(this.ctx, this.opt.iframes, this.opt.exclude, this.opt.iframesTimeout);
          }
        }]);
        return Mark2;
      }();
      function Mark(ctx) {
        var _this = this;
        var instance = new Mark$1(ctx);
        this.mark = function(sv, opt) {
          instance.mark(sv, opt);
          return _this;
        };
        this.markRegExp = function(sv, opt) {
          instance.markRegExp(sv, opt);
          return _this;
        };
        this.markRanges = function(sv, opt) {
          instance.markRanges(sv, opt);
          return _this;
        };
        this.unmark = function(opt) {
          instance.unmark(opt);
          return _this;
        };
        return this;
      }
      return Mark;
    });
  }
});

// src/ui/solid/tree.tsx
var require_tree = __commonJS({
  "src/ui/solid/tree.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.Tree = Tree;
    var _web = require_web();
    var _solidJs = require_solid();
    var _branch = require_branch();
    var _collapseEmptyNodes = (init_collapse_empty_nodes(), __toCommonJS(collapse_empty_nodes_exports));
    var _mark = _interopRequireDefault(require_mark());
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    var _tmpl$ = /* @__PURE__ */ (0, _web.template)(`<div>`);
    function Tree(props) {
      const collapsedTree = () => ({
        ...props.fileContextTree,
        branches: props.fileContextTree.branches.map(_collapseEmptyNodes.collapseEmptyNodes)
      });
      let markContextRef;
      (0, _solidJs.createEffect)(() => {
        new _mark.default(markContextRef).mark(props.highlights, {
          element: "span",
          className: "search-result-file-matched-text",
          separateWordSearch: false,
          diacritics: false
        });
      });
      return (
        // @ts-ignore
        (() => {
          const _el$ = _tmpl$();
          const _ref$ = markContextRef;
          typeof _ref$ === "function" ? (0, _web.use)(_ref$, _el$) : markContextRef = _el$;
          (0, _web.insert)(_el$, (0, _web.createComponent)(_branch.Branch, {
            get contextTree() {
              return collapsedTree();
            }
          }));
          return _el$;
        })()
      );
    }
  }
});

// src/ui/solid/render-context-tree.tsx
var require_render_context_tree = __commonJS({
  "src/ui/solid/render-context-tree.tsx"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.renderContextTree = renderContextTree2;
    var _web = require_web();
    var _pluginContext = require_plugin_context();
    var _tree = require_tree();
    function renderContextTree2({
      contextTree,
      el,
      plugin,
      highlights,
      infinityScroll
    }) {
      return (0, _web.render)(() => (0, _web.createComponent)(_pluginContext.PluginContextProvider, {
        plugin,
        infinityScroll,
        get children() {
          return (0, _web.createComponent)(_tree.Tree, {
            fileContextTree: contextTree,
            highlights
          });
        }
      }), el);
    }
  }
});

// src/plugin.ts
var plugin_exports = {};
__export(plugin_exports, {
  default: () => BetterBacklinksPlugin
});
module.exports = __toCommonJS(plugin_exports);
var import_obsidian2 = require("obsidian");

// src/patcher.ts
var import_obsidian = require("obsidian");

// node_modules/monkey-around/mjs/index.js
function around(obj, factories) {
  const removers = Object.keys(factories).map((key) => around1(obj, key, factories[key]));
  return removers.length === 1 ? removers[0] : function() {
    removers.forEach((r) => r());
  };
}
function around1(obj, method, createWrapper) {
  const original = obj[method], hadOwn = obj.hasOwnProperty(method);
  let current = createWrapper(original);
  if (original)
    Object.setPrototypeOf(current, original);
  Object.setPrototypeOf(wrapper, current);
  obj[method] = wrapper;
  return remove;
  function wrapper(...args) {
    if (current === original && obj[method] === wrapper)
      remove();
    return current.apply(this, args);
  }
  function remove() {
    if (obj[method] === wrapper) {
      if (hadOwn)
        obj[method] = original;
      else
        delete obj[method];
    }
    if (current === original)
      return;
    current = original;
    Object.setPrototypeOf(wrapper, original || Function);
  }
}

// src/metadata-cache-util/position.ts
var getTextAtPosition = (textInput, pos) => textInput.substring(pos.start.offset, pos.end.offset);
var getTextFromLineStartToPositionEnd = (textInput, pos) => textInput.substring(pos.start.offset - pos.start.col, pos.end.offset);
var doesPositionIncludeAnother = (container, child) => container.start.offset <= child.start.offset && container.end.offset >= child.end.offset;
function isSamePosition(a, b) {
  return a && b && a.start.offset === b.start.offset && a.end.offset === b.end.offset;
}
function createPositionFromOffsets(content, startOffset, endOffset) {
  const startLine = content.substring(0, startOffset).split("\n").length - 1;
  const endLine = content.substring(0, endOffset).split("\n").length - 1;
  const startLinePos = content.substring(0, startOffset).lastIndexOf("\n") + 1;
  const startCol = content.substring(startLinePos, startOffset).length;
  const endLinePos = content.substring(0, endOffset).lastIndexOf("\n") + 1;
  const endCol = content.substring(endLinePos, endOffset).length;
  return {
    position: {
      start: { line: startLine, col: startCol, offset: startOffset },
      end: { line: endLine, col: endCol, offset: endOffset }
    }
  };
}

// src/metadata-cache-util/heading.ts
function getHeadingIndexContaining(position, headings) {
  return headings.findIndex(
    (heading) => heading.position.start.line === position.start.line
  );
}
function getIndexOfHeadingAbove(position, headings) {
  return headings.reduce(
    (previousIndex, lookingAtHeading, index) => lookingAtHeading.position.start.line < position.start.line ? index : previousIndex,
    -1
  );
}
function getHeadingBreadcrumbs(position, headings) {
  const headingBreadcrumbs = [];
  if (headings.length === 0) {
    return headingBreadcrumbs;
  }
  const collectAncestorHeadingsForHeadingAtIndex = (startIndex) => {
    let currentLevel = headings[startIndex].level;
    const previousHeadingIndex = startIndex - 1;
    for (let i = previousHeadingIndex; i >= 0; i--) {
      const lookingAtHeading = headings[i];
      if (lookingAtHeading.level < currentLevel) {
        currentLevel = lookingAtHeading.level;
        headingBreadcrumbs.unshift(lookingAtHeading);
      }
    }
  };
  const headingIndexAtPosition = getHeadingIndexContaining(position, headings);
  const positionIsInsideHeading = headingIndexAtPosition >= 0;
  if (positionIsInsideHeading) {
    headingBreadcrumbs.unshift(headings[headingIndexAtPosition]);
    collectAncestorHeadingsForHeadingAtIndex(headingIndexAtPosition);
    return headingBreadcrumbs;
  }
  const headingIndexAbovePosition = getIndexOfHeadingAbove(position, headings);
  const positionIsBelowHeading = headingIndexAbovePosition >= 0;
  if (positionIsBelowHeading) {
    const headingAbovePosition = headings[headingIndexAbovePosition];
    headingBreadcrumbs.unshift(headingAbovePosition);
    collectAncestorHeadingsForHeadingAtIndex(headingIndexAbovePosition);
    return headingBreadcrumbs;
  }
  return headingBreadcrumbs;
}

// src/metadata-cache-util/list.ts
function getListItemWithDescendants(listItemIndex, listItems) {
  const rootListItem = listItems[listItemIndex];
  const listItemWithDescendants = [rootListItem];
  for (let i = listItemIndex + 1; i < listItems.length; i++) {
    const nextItem = listItems[i];
    if (nextItem.parent < rootListItem.position.start.line) {
      return listItemWithDescendants;
    }
    listItemWithDescendants.push(nextItem);
  }
  return listItemWithDescendants;
}
function getListBreadcrumbs(position, listItems) {
  const listBreadcrumbs = [];
  if (listItems.length === 0) {
    return listBreadcrumbs;
  }
  const thisItemIndex = getListItemIndexContaining(position, listItems);
  const isPositionOutsideListItem = thisItemIndex < 0;
  if (isPositionOutsideListItem) {
    return listBreadcrumbs;
  }
  const thisItem = listItems[thisItemIndex];
  let currentParent = thisItem.parent;
  if (isTopLevelListItem(thisItem)) {
    return listBreadcrumbs;
  }
  for (let i = thisItemIndex - 1; i >= 0; i--) {
    const currentItem = listItems[i];
    const currentItemIsHigherUp = currentItem.parent < currentParent;
    if (currentItemIsHigherUp) {
      listBreadcrumbs.unshift(currentItem);
      currentParent = currentItem.parent;
    }
    if (isTopLevelListItem(currentItem)) {
      return listBreadcrumbs;
    }
  }
  return listBreadcrumbs;
}
function isTopLevelListItem(listItem) {
  return listItem.parent < 0;
}
function getListItemIndexContaining(searchedForPosition, listItems) {
  return listItems.findIndex(
    ({ position }) => doesPositionIncludeAnother(position, searchedForPosition)
  );
}
function isPositionInList(position, listItems) {
  return getListItemIndexContaining(position, listItems) >= 0;
}

// src/metadata-cache-util/section.ts
function getSectionContaining(searchedForPosition, sections) {
  return sections.find(
    ({ position }) => doesPositionIncludeAnother(position, searchedForPosition)
  );
}
function getFirstSectionUnder(position, sections) {
  return sections.find(
    (section) => section.position.start.line > position.start.line
  );
}

// src/metadata-cache-util/format.ts
var formatListWithDescendants = (textInput, listItems) => {
  const root = listItems[0];
  const leadingSpacesCount = root.position.start.col;
  return listItems.map(
    (itemCache) => getTextFromLineStartToPositionEnd(textInput, itemCache.position).slice(
      leadingSpacesCount
    )
  ).join("\n");
};

// src/context-tree/create/create-context-tree.ts
function createContextTree({
  positions,
  fileContents,
  stat,
  filePath,
  listItems = [],
  headings = [],
  sections = []
}) {
  const positionsWithContext = positions.map((position) => {
    return {
      headingBreadcrumbs: getHeadingBreadcrumbs(position.position, headings),
      listBreadcrumbs: getListBreadcrumbs(position.position, listItems),
      sectionCache: getSectionContaining(position.position, sections),
      position
    };
  });
  const root = createContextTreeBranch("file", {}, stat, filePath, filePath);
  for (const {
    headingBreadcrumbs,
    listBreadcrumbs,
    sectionCache,
    position
  } of positionsWithContext) {
    if (!sectionCache) {
      continue;
    }
    let context = root;
    for (const headingCache of headingBreadcrumbs) {
      const headingFoundInChildren = context.branches.find(
        (tree) => isSamePosition(tree.cacheItem.position, headingCache.position)
      );
      if (headingFoundInChildren) {
        context = headingFoundInChildren;
      } else {
        const newContext = createContextTreeBranch(
          "heading",
          headingCache,
          stat,
          filePath,
          headingCache.heading
        );
        context.branches.push(newContext);
        context = newContext;
      }
    }
    for (const listItemCache of listBreadcrumbs) {
      const listItemFoundInChildren = context.branches.find(
        (tree) => isSamePosition(tree.cacheItem.position, listItemCache.position)
      );
      if (listItemFoundInChildren) {
        context = listItemFoundInChildren;
      } else {
        const newListContext = createContextTreeBranch(
          "list",
          listItemCache,
          stat,
          filePath,
          getTextAtPosition(fileContents, listItemCache.position)
        );
        context.branches.push(newListContext);
        context = newListContext;
      }
    }
    const headingIndexAtPosition = getHeadingIndexContaining(
      position.position,
      headings
    );
    const linkIsInsideHeading = headingIndexAtPosition >= 0;
    if (isPositionInList(position.position, listItems)) {
      const indexOfListItemContainingLink = getListItemIndexContaining(
        position.position,
        listItems
      );
      const listItemCacheWithDescendants = getListItemWithDescendants(
        indexOfListItemContainingLink,
        listItems
      );
      const text = formatListWithDescendants(
        fileContents,
        listItemCacheWithDescendants
      );
      context.sectionsWithMatches.push({
        // TODO: add type to the cache
        // @ts-ignore
        cache: listItemCacheWithDescendants[0],
        text,
        filePath
      });
    } else if (linkIsInsideHeading) {
      const firstSectionUnderHeading = getFirstSectionUnder(
        position.position,
        sections
      );
      if (firstSectionUnderHeading) {
        context.sectionsWithMatches.push({
          cache: firstSectionUnderHeading,
          text: getTextAtPosition(
            fileContents,
            firstSectionUnderHeading.position
          ),
          filePath
        });
      }
    } else {
      const sectionText = getTextAtPosition(
        fileContents,
        sectionCache.position
      );
      context.sectionsWithMatches.push({
        cache: sectionCache,
        text: sectionText,
        filePath
      });
    }
  }
  return root;
}
function createContextTreeBranch(type, cacheItem, stat, filePath, text) {
  return {
    type,
    cacheItem,
    filePath,
    text,
    stat,
    branches: [],
    sectionsWithMatches: []
  };
}

// src/patcher.ts
var import_render_context_tree = __toESM(require_render_context_tree());
init_patterns();

// src/disposer-registry.ts
var DisposerRegistry = class {
  constructor() {
    this.domToDisposers = /* @__PURE__ */ new WeakMap();
  }
  /**
   * We assume here that match results are going to be added synchronously to the same dom, on which onAddResult is
   * called
   * @param searchResultDom
   */
  onAddResult(searchResultDom) {
    this.contextDom = searchResultDom;
    if (!this.domToDisposers.has(this)) {
      this.domToDisposers.set(this, []);
    }
  }
  addOnEmptyResultsCallback(fn) {
    var _a;
    if (!this.contextDom) {
      throw new Error(
        "You rendered a Solid root before you got a reference to the containing searchResultDom"
      );
    }
    (_a = this.domToDisposers.get(this.contextDom)) == null ? void 0 : _a.push(fn);
  }
  /**
   * This may be called before any results are added
   * @param searchResultDom
   */
  onEmptyResults(searchResultDom) {
    var _a;
    (_a = this.domToDisposers.get(searchResultDom)) == null ? void 0 : _a.forEach((disposer) => disposer());
    this.domToDisposers.set(searchResultDom, []);
  }
};

// src/context-tree/dedupe/dedupe-matches.ts
function dedupeMatches(tree) {
  return {
    ...tree,
    sectionsWithMatches: dedupe(tree.sectionsWithMatches),
    branches: tree.branches.map((branch) => dedupeMatches(branch))
  };
}
function areMatchesInSameSection(a, b) {
  return a.text === b.text && isSamePosition(a.cache.position, b.cache.position);
}
function dedupe(matches) {
  return matches.filter(
    (match, index, array) => index === array.findIndex((inner) => areMatchesInSameSection(inner, match))
  );
}

// src/patcher.ts
var errorTimeout = 1e4;
function getHighlightsFromVChild(vChild) {
  const { content, matches } = vChild;
  const firstMatch = matches[0];
  const [start, end] = firstMatch;
  return content.substring(start, end).toLowerCase().replace(wikiLinkBrackets, "");
}
var Patcher = class {
  constructor(plugin) {
    this.plugin = plugin;
    this.wrappedMatches = /* @__PURE__ */ new WeakSet();
    this.wrappedSearchResultItems = /* @__PURE__ */ new WeakSet();
    this.triedPatchingSearchResultItem = false;
    this.triedPatchingRenderContentMatches = false;
    this.disposerRegistry = new DisposerRegistry();
  }
  patchComponent() {
    const patcher = this;
    this.plugin.register(
      around(import_obsidian.Component.prototype, {
        addChild(old) {
          return function(child, ...args) {
            const thisIsSearchView = this.hasOwnProperty("searchQuery");
            if (thisIsSearchView && !patcher.triedPatchingSearchResultItem) {
              patcher.triedPatchingSearchResultItem = true;
              try {
                patcher.patchSearchResultDom(child.dom);
              } catch (error) {
                patcher.reportError(
                  error,
                  "Error while patching Obsidian internals"
                );
              }
            }
            return old.call(this, child, ...args);
          };
        }
      })
    );
  }
  patchSearchResultDom(searchResultDom) {
    const patcher = this;
    this.plugin.register(
      around(searchResultDom.constructor.prototype, {
        addResult(old) {
          return function(...args) {
            patcher.disposerRegistry.onAddResult(this);
            const result = old.call(this, ...args);
            if (!patcher.triedPatchingRenderContentMatches) {
              patcher.triedPatchingRenderContentMatches = true;
              try {
                patcher.patchSearchResultItem(result);
              } catch (error) {
                patcher.reportError(
                  error,
                  "Error while patching Obsidian internals"
                );
              }
            }
            return result;
          };
        },
        emptyResults(old) {
          return function(...args) {
            patcher.disposerRegistry.onEmptyResults(this);
            return old.call(this, ...args);
          };
        }
      })
    );
  }
  patchSearchResultItem(searchResultItem) {
    const patcher = this;
    this.plugin.register(
      around(searchResultItem.constructor.prototype, {
        renderContentMatches(old) {
          return function(...args) {
            const result = old.call(this, ...args);
            if (patcher.wrappedSearchResultItems.has(this) || !this.vChildren._children || this.vChildren._children.length === 0) {
              return result;
            }
            patcher.wrappedSearchResultItems.add(this);
            try {
              let someMatchIsInProperties = false;
              const matchPositions = this.vChildren._children.map(
                // todo: works only for one match per block
                (child) => {
                  const { content, matches } = child;
                  const firstMatch2 = matches[0];
                  if (Object.hasOwn(firstMatch2, "key")) {
                    someMatchIsInProperties = true;
                    return null;
                  }
                  const [start, end] = firstMatch2;
                  return createPositionFromOffsets(content, start, end);
                }
              );
              if (someMatchIsInProperties) {
                return result;
              }
              const highlights = this.vChildren._children.map(
                getHighlightsFromVChild
              );
              const deduped = [...new Set(highlights)];
              const firstMatch = this.vChildren._children[0];
              patcher.mountContextTreeOnMatchEl(
                this,
                firstMatch,
                matchPositions,
                deduped,
                this.parent.infinityScroll
              );
              this.vChildren._children = this.vChildren._children.slice(0, 1);
            } catch (e) {
              patcher.reportError(
                e,
                `Failed to mount context tree for file path: ${this.file.path}`
              );
            }
            return result;
          };
        }
      })
    );
  }
  reportError(error, message) {
    var _a;
    (_a = this.currentNotice) == null ? void 0 : _a.hide();
    this.currentNotice = new import_obsidian.Notice(
      `Better Search Views: ${message}. Please report an issue with the details from the console attached.`,
      errorTimeout
    );
    console.error(`${message}. Reason:`, error);
  }
  mountContextTreeOnMatchEl(container, match, positions, highlights, infinityScroll) {
    if (this.wrappedMatches.has(match)) {
      return;
    }
    this.wrappedMatches.add(match);
    const { cache, content } = match;
    const { file } = container;
    const matchIsOnlyInFileName = !cache.sections || content === "";
    if (file.extension === "canvas" || matchIsOnlyInFileName) {
      return;
    }
    const contextTree = createContextTree({
      positions,
      fileContents: content,
      stat: file.stat,
      filePath: file.path,
      ...cache
    });
    const mountPoint = createDiv();
    const dispose = (0, import_render_context_tree.renderContextTree)({
      highlights,
      contextTree: dedupeMatches(contextTree),
      el: mountPoint,
      plugin: this.plugin,
      infinityScroll
    });
    this.disposerRegistry.addOnEmptyResultsCallback(dispose);
    match.el = mountPoint;
  }
};

// src/plugin.ts
var BetterBacklinksPlugin = class extends import_obsidian2.Plugin {
  async onload() {
    const patcher = new Patcher(this);
    patcher.patchComponent();
  }
};
/*! Bundled license information:

mark.js/dist/mark.js:
  (*!***************************************************
  * mark.js v8.11.1
  * https://markjs.io/
  * Copyright (c) 2014–2018, Julian Kühnel
  * Released under the MIT license https://git.io/vwTVl
  *****************************************************)
*/
