{"dualConfigs": false, "showConsoleLog": false, "desktop": {"shortDelaySeconds": 10, "longDelaySeconds": 25, "delayBetweenPlugins": 40, "defaultStartupType": null, "showDescriptions": true, "plugins": {"obsidian-admonition": {"startupType": "short"}, "attachment-manager": {"startupType": "instant"}, "obsidian-auto-link-title": {"startupType": "instant"}, "better-search-views": {"startupType": "instant"}, "obsidian42-brat": {"startupType": "instant"}, "calendar": {"startupType": "instant"}, "obsidian-citation-plugin": {"startupType": "short"}, "code-styler": {"startupType": "short"}, "copilot": {"startupType": "short"}, "obsidian-copy-block-link": {"startupType": "instant"}, "obsidian-custom-frames": {"startupType": "instant"}, "customjs": {"startupType": "instant"}, "dataview": {"startupType": "instant"}, "dbfolder": {"startupType": "instant"}, "easy-typing-obsidian": {"startupType": "instant"}, "execute-code": {"startupType": "instant"}, "floating-toc": {"startupType": "instant"}, "fuzzy-chinese-pinyin": {"startupType": "instant"}, "heatmap-calendar": {"startupType": "instant"}, "homepage": {"startupType": "instant"}, "obsidian-image-auto-upload-plugin": {"startupType": "instant"}, "periodic-para": {"startupType": "instant"}, "obsidian-markmind": {"startupType": "instant"}, "smart-connections": {"startupType": "short"}, "obsidian-style-settings": {"startupType": "instant"}, "supercharged-links-obsidian": {"startupType": "instant"}, "tag-wrangler": {"startupType": "instant"}, "obsidian-tasks-plugin": {"startupType": "instant"}, "templater-obsidian": {"startupType": "short"}, "obsidian-toggl-integration": {"startupType": "short"}, "obsidian-zotero-desktop-connector": {"startupType": "short"}, "readwise-official": {"startupType": "instant"}, "obsidian-advanced-uri": {"startupType": "instant"}, "obsidian-mindmap-nextgen": {"startupType": "instant"}, "note-refactor-obsidian": {"startupType": "short"}, "components": {"startupType": "instant"}, "media-extended": {"startupType": "instant"}, "obsidian-outliner": {"startupType": "instant"}, "number-headings-obsidian": {"startupType": "short"}, "obsidian-hover-editor": {"startupType": "disabled"}, "obsidian-plugin-toc": {"startupType": "instant"}, "obsidian-shellcommands": {"startupType": "instant"}, "zotlit": {"startupType": "short"}, "obsidian-meta-bind-plugin": {"startupType": "instant"}, "obsidian-export-image": {"startupType": "instant"}, "obsidian-gdrive-sync": {"startupType": "disabled"}, "cubox-sync": {"startupType": "disabled"}, "obsidian-weread-plugin": {"startupType": "instant"}, "ai-bridge": {"startupType": "instant"}, "rss-dashboard": {"startupType": "instant"}, "ai-prompt-manager": {"startupType": "disabled"}}}}